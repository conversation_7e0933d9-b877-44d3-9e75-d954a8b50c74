package com.trs.police.lz.authCenter.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import com.trs.police.common.core.handler.typehandler.JsonToFileInfoHandler;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 权限中心-后台管理表
 *
 * <AUTHOR>
 * @date 2025/04/01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_auth_center_backend_manage", autoResultMap = true)
public class AuthCenterBackendManage extends AbstractBaseEntity {

    /**
     * 标题
     */
    @TableField(value = "name")
    private String name;

    /**
     * 内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 类型 1：通知通报，2：技战法，3：典型案例
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 状态 0：选用；1：未选用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 附件
     */
    @TableField(typeHandler = JsonToFileInfoHandler.class)
    private List<FileInfoVO> attachments;
}
