package com.trs.police.ga.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.ga.entity.GaZhiBanBaoBeiEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @since ：2025/3/21 15:07
 * @version 1.0
 * @since 1.0
 */
@DS("ga-qwgl")
@Mapper
public interface GaZhiBanBaoBeiMapper extends BaseMapper<GaZhiBanBaoBeiEntity> {

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param condition 查询条件
     * @return 结果
     */
    Page<GaZhiBanBaoBeiEntity> doPageSelect(Page<GaZhiBanBaoBeiEntity> page,
                                            @Param("condition") String condition);

    /**
     * 总计
     *
     * @param condition 查询条件
     * @return 结果
     */
    Long selectCount(@Param("condition") String condition);
}
