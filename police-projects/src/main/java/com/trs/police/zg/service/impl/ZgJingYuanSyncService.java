package com.trs.police.zg.service.impl;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.bigscreen.domain.entity.BigScreenJingYuanEntity;
import com.trs.police.bigscreen.domain.vo.RongHeSheBeiVo;
import com.trs.police.bigscreen.mapper.BigScreenJingYuanMapper;
import com.trs.police.bigscreen.service.BaseJingYuanSyncService;
import com.trs.police.common.core.mapper.SyncTaskMapper;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.zg.utils.QinWuUtils;
import com.trs.police.zg.vo.*;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/9/6 14:31
 * @since 1.0
 */
@Service
@Slf4j
@ConditionalOnProperty(value = "com.trs.bigscreen.system.area", havingValue = "zg")
public class ZgJingYuanSyncService extends BaseJingYuanSyncService<BigScreenJingYuanEntity> {

    public ZgJingYuanSyncService(
            BigScreenJingYuanMapper jingYuanMapper,
            SyncTaskMapper syncTaskMapper,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(jingYuanMapper, syncTaskMapper, redisTemplate);
    }

    /**
     * findBaoBei<BR>
     *
     * @param sql 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:24
     */
    private List<XunFangBaoBeiVo> findBaoBei(String sql) throws ServiceException {
        int pageNum = 1;
        Tuple2<Long, List<XunFangBaoBeiVo>> t = qinWuUtils.qwglXfbb(pageNum, QinWuUtils.PAGE_SIZE, sql);
        final int size = t._1.intValue();
        List<XunFangBaoBeiVo> d = new ArrayList<>(size);
        d.addAll(t._2);
        while (size > QinWuUtils.PAGE_SIZE * pageNum) {
            pageNum += 1;
            d.addAll(qinWuUtils.qwglXfbb(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
        }
        return d;
    }

    /**
     * findQwglXfbbZd<BR>
     *
     * @param xfbbId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/18 18:10
     */
    private List<XunFangBaoBeiZhongDuanVo> findQwglXfbbZd(String xfbbId) {
        if (StringUtils.isEmpty(xfbbId)) {
            return List.of();
        }
        try {
            int pageNum = 1;
            final var sql = String.format(" xt_scbz = '0' and xfbb_id = '%s' ", xfbbId);
            Tuple2<Long, List<XunFangBaoBeiZhongDuanVo>> t = qinWuUtils.qwglXfbbZd(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            List<XunFangBaoBeiZhongDuanVo> d = new ArrayList<>(size);
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.qwglXfbbZd(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
            return d;
        } catch (Exception e) {
            log.error("根据[{}]查询终端异常", xfbbId, e);
            return List.of();
        }
    }

    /**
     * findLastSheBei<BR>
     *
     * @param jzId 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 15:31
     */
    private Optional<SheBeiLiShiWeiZhiVo> findLastSheBei(String jzId) {
        if (StringUtils.isEmpty(jzId)) {
            return Optional.empty();
        }
        List<SheBeiLiShiWeiZhiVo> d = new ArrayList<>(0);
        Try.run(() -> {
            final var time = getCurrentDate("yyyy-MM-dd 00:00:00");
            final var sql = String.format(" xt_cjsj >= '%s' and xt_scbz = '0' and jz_id = '%s' ", time, jzId);
            int pageNum = 1;
            Tuple2<Long, List<SheBeiLiShiWeiZhiVo>> t = qinWuUtils.wzSb(pageNum, QinWuUtils.PAGE_SIZE, sql);
            final int size = t._1.intValue();
            d.addAll(t._2);
            while (size > QinWuUtils.PAGE_SIZE * pageNum) {
                pageNum += 1;
                d.addAll(qinWuUtils.wzSb(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
            }
        }).onFailure(e -> log.error("根据警组[{}]查询最后设备异常", jzId, e));
        return d.stream().max(Comparator.comparing(BaseQingWuVo::getXtCjsj));
    }

    /**
     * findXunFangBaoBeiRenYuan<BR>
     *
     * @param sql 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/17 14:20
     */
    private List<XunFangBaoBeiRenYuanVo> findXunFangBaoBeiRenYuan(String sql) throws ServiceException {
        int pageNum = 1;
        Tuple2<Long, List<XunFangBaoBeiRenYuanVo>> t = qinWuUtils.xfbbRy(pageNum, QinWuUtils.PAGE_SIZE, sql);
        final int size = t._1.intValue();
        List<XunFangBaoBeiRenYuanVo> ry = new ArrayList<>(size);
        ry.addAll(t._2);
        while (size > QinWuUtils.PAGE_SIZE * pageNum) {
            pageNum += 1;
            ry.addAll(qinWuUtils.xfbbRy(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
        }
        return ry;
    }

    /**
     * defaultStartTime<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 19:11
     */
    @Override
    public String defaultStartTime() {
        return null;
    }

    /**
     * findAllRenYuanData<BR>
     *
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/25 19:54
     */
    private List<BigScreenJingYuanEntity> findAllRenYuanData(String startTime, String endTime)
            throws ServiceException {
        List<String> time1 = new ArrayList<>(2);
        List<String> time2 = new ArrayList<>(2);
        if (StringUtils.isNotEmpty(startTime)) {
            time1.add(String.format(" xt_cjsj >= '%s' ", startTime));
            time2.add(String.format(" xt_zhgxsj >= '%s' ", startTime));
        }
        if (StringUtils.isNotEmpty(endTime)) {
            time1.add(String.format(" xt_cjsj <= '%s' ", endTime));
            time2.add(String.format(" xt_zhgxsj <= '%s' ", endTime));
        }
        final var sql = Stream.of(time1, time2)
                .map(it -> "(" + String.join(" and ", it) + ")")
                .collect(Collectors.joining(" or "));
        int pageNum = 1;
        Tuple2<Long, List<XunFangLiLiangVo>> t = qinWuUtils.xfll(pageNum, QinWuUtils.PAGE_SIZE, sql);
        final int size = t._1.intValue();
        List<XunFangLiLiangVo> ry = new ArrayList<>(size);
        ry.addAll(t._2);
        while (size > QinWuUtils.PAGE_SIZE * pageNum) {
            pageNum += 1;
            ry.addAll(qinWuUtils.xfll(pageNum, QinWuUtils.PAGE_SIZE, sql)._2);
        }
        log.info("[{}]查询人员数据,共{}条", sql, ry.size());
        return ry.stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * findInData<BR>
     *
     * @param startTime 参数
     * @param endTime   参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:05
     */
    @Override
    public Tuple2<List<BigScreenJingYuanEntity>, String> findInData(String startTime, String endTime) {
        final Date now = new Date();
        final var dutyTime = dateToString(now, TimeUtils.YYYYMMDD);
        final var time = dateToString(now, TimeUtils.YYYYMMDD_HHMMSS);
        final List<BigScreenJingYuanEntity> all = new ArrayList<>();
        Try.run(() -> all.addAll(findAllRenYuanData(startTime, endTime)))
                .onFailure(e -> log.error("查询全部人员数据异常", e));
        final var sql = String.format(" xt_scbz = '0' and bb_sj_kssj <= '%s' and bb_sj_jssj >= '%s' ", time, time);
        List<BigScreenJingYuanEntity> list = Try.of(() -> {
            var map = findBaoBei(sql).stream()
                    .filter(it -> Objects.nonNull(it.getBcKssj())
                            && Objects.nonNull(it.getBcJssj())
                            && StringUtils.isNotEmpty(it.getId())
                    ).peek(it -> {
                        it.setBcKssj(stringToDate(String.format("%s %s", dutyTime, dateToString(it.getBcKssj(), TimeUtils.HHMMSS))));
                        it.setBcJssj(stringToDate(String.format("%s %s", dutyTime, dateToString(it.getBcJssj(), TimeUtils.HHMMSS))));
                    }).filter(it -> it.getBcKssj().before(now) && it.getBcJssj().after(now))
                    .collect(Collectors.groupingBy(XunFangBaoBeiVo::getId));
            if (map.isEmpty()) {
                return new ArrayList<BigScreenJingYuanEntity>();
            }
            String ids = map.keySet()
                    .stream()
                    .map(it -> String.format("'%s'", it))
                    .collect(Collectors.joining(StringUtils.SEPARATOR_COMMA));
            String zbbbRySql = String.format(" xt_scbz = '0' and xfbb_id in (%s) ", ids);
            final Map<String, List<XunFangBaoBeiZhongDuanVo>> xfsbMapping = new HashMap<>(map.size());
            return findXunFangBaoBeiRenYuan(zbbbRySql).stream()
                    .filter(it -> map.containsKey(it.getXfbbId()))
                    .map(it -> {
                        final XunFangBaoBeiVo bb = map.get(it.getXfbbId()).get(0);
                        if (!xfsbMapping.containsKey(it.getXfbbId())) {
                            xfsbMapping.put(it.getXfbbId(), findQwglXfbbZd(it.getXfbbId()));
                        }
                        final List<XunFangBaoBeiZhongDuanVo> zd = xfsbMapping.getOrDefault(it.getXfbbId(), List.of());
                        final var sb = findLastSheBei(bb.getJzId());
                        final var xfqy = qinWuUtils.qwglXfqyOne(bb.getXfqyId());
                        BigScreenJingYuanEntity entity = convert(qinWuUtils.xfllOne(it.getXfllId()));
                        if (Objects.isNull(entity)) {
                            log.warn("人员数据[{}]转换失败，跳过", it);
                            return null;
                        }
                        entity.setUnitName(bb.getSsbm());
                        entity.setDistrictCode(bb.getSsbmdm().substring(0, 6));
                        entity.setZsz(bb.getBcMc());
                        entity.setCjzt(1);
                        entity.setBbzt(1);
                        entity.setZxzt(1);
                        entity.setStatus(1);
                        entity.setRhyxsb(JsonUtil.toJsonString(
                                zd.stream()
                                        .map(z -> RongHeSheBeiVo.builder()
                                                .name(z.getSbmc())
                                                .bh(z.getSbbh())
                                                .lx(z.getSblx())
                                                .lb(z.getSblb())
                                                .build())
                                        .collect(Collectors.toList())
                        ));
                        if (sb.isPresent()) {
                            entity.setDqwzJd(sb.get().getJd());
                            entity.setDqwzWd(sb.get().getWd());
                            entity.setDqwzMc(StringUtils.showEmpty(sb.get().getDzjc(), sb.get().getDzxz()));
                        } else if (Objects.nonNull(xfqy)) {
                            entity.setDqwzJd(xfqy.getZxdJd());
                            entity.setDqwzWd(xfqy.getZxdWd());
                            entity.setDqwzMc(xfqy.getXqmc());
                        }
                        if (StringUtils.isEmpty(entity.getDqwzMc())) {
                            entity.setDqwzMc(bb.getXfqyMc());
                        }
                        if (Objects.nonNull(it.getXtZhgxsj())) {
                            entity.setLastReportTime(it.getXtZhgxsj());
                        } else if (Objects.nonNull(it.getXtCjsj())) {
                            entity.setLastReportTime(it.getXtCjsj());
                        } else {
                            entity.setLastReportTime(new Date());
                        }
                        if (StringUtils.isEmpty(entity.getDh())) {
                            entity.setDh(StringUtils.showEmpty(bb.getZbdh()));
                        }
                        return entity;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
        }).onFailure(e -> log.error("[{}]同步异常", desc(), e)).getOrElse(new ArrayList<>());
        all.addAll(list);
        return new Tuple2<>(all, sql);
    }

    /**
     * convert<BR>
     *
     * @param one 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/12/25 19:26
     */
    private BigScreenJingYuanEntity convert(XunFangLiLiangVo one) {
        try {
            if (Objects.isNull(one)) {
                return null;
            }
            if (StringUtils.isEmpty(one.getJh())
                    && StringUtils.isEmpty(one.getSfzh())) {
                log.warn("人员数据[{}]没有警号跟证件号码，跳过", one);
                return null;
            }
            BigScreenJingYuanEntity entity = new BigScreenJingYuanEntity();
            entity.setUnitName(one.getSsbm());
            entity.setDistrictCode(one.getSsbmdm().substring(0, 6));
            entity.setCjzt(0);
            entity.setBbzt(0);
            entity.setZxzt(0);
            entity.setStatus(0);
            entity.setProfilePic(one.getTp());
            entity.setLastReportTime(new Date());
            entity.setXm(one.getXm());
            entity.setJh(StringUtils.showEmpty(one.getJh(), one.getSfzh()));
            entity.setDh(StringUtils.showEmpty(one.getLxdh()));
            entity.setRylx(QinWuUtils.convertRenYuanLeiXin(one.getFl()));
            return entity;
        } catch (Exception e) {
            log.warn("转换人员[{}]异常", one, e);
            return null;
        }
    }

    /**
     * convert<BR>
     *
     * @param in 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/9/5 18:04
     */
    @Override
    public BigScreenJingYuanEntity convert(BigScreenJingYuanEntity in) {
        return in;
    }

    @Override
    public String key() {
        return "ZgJingYuan";
    }

    @Override
    public String desc() {
        return "自贡JY信息同步";
    }
}
