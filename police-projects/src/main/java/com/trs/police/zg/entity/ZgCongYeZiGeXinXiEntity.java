package com.trs.police.zg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 从业资格信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName(value = "tb_projects_zg_cyzgxx", autoResultMap = true)
public class ZgCongYeZiGeXinXiEntity {

    /**
     * 证件类型
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 信息主键编号
     */
    @TableField
    private String xxzjbh;

    /**
     * 业务主键
     */
    @TableField
    private String ywid;

    /**
     * 姓名
     */
    @TableField
    private String xm;

    /**
     * 证件号码
     */
    @TableField
    private String zjhm;

    /**
     * 出生日期
     */
    @TableField
    private Date csrq;

    /**
     * 联系电话
     */
    @TableField
    private String lxdh;

    /**
     * 从业资格证号
     */
    @TableField
    private String cyzgzh;

    /**
     * 从业资格类别1(字典:shzy_ldjy_cyzglb)
     */
    @TableField
    private String cyzglb1;

    /**
     * 从业资格级别
     */
    @TableField
    private String cyzgjb;

    /**
     * 颁发机构名称
     */
    @TableField
    private String bfjgmc;

    /**
     * 从业资格证发证日期1
     */
    @TableField
    private Date cyzgzfzrq1;

    /**
     * 从业资格证有效结束日期1
     */
    @TableField
    private Date cyzgzyxjsrq1;

    /**
     * 主要执业机构名称
     */
    @TableField
    private String zycyjgmc;

    /**
     * 从业资格适用范围
     */
    @TableField
    private String cyzgsyfw;

    /**
     * 首次入库时间
     */
    @TableField
    private Date scrksj;

    /**
     * 信息备注
     */
    @TableField
    private String xxbz;

    /**
     * 信息来源地区划
     */
    @TableField
    private String xxlydqh;

    /**
     * 信息来源描述
     */
    @TableField
    private String xxlyms;

    /**
     * 数据来源编号
     */
    @TableField
    private String sjlybh;

    /**
     * 数据标识
     */
    @TableField
    private String depActionFlag;

    /**
     * 数据抽取时间
     */
    @TableField
    private Date depActionTime;

    @TableField
    private Date depFirstenterTime;

    /**
     * 性别
     */
    @TableField
    private String xb;

    /**
     * 国籍
     */
    @TableField
    private String gj;

    /**
     * 住址
     */
    @TableField
    private String zz;

    /**
     * 证件类型(字典:shzy_ldjy_cyzglb)
     */
    @TableField
    private String zjlx;

    /**
     * 办理类型
     */
    @TableField
    private String bllx;

    /**
     * 原证件编号
     */
    @TableField
    private String yzjbh;

    /**
     * 从业资格证有效起始日期1
     */
    @TableField
    private Date cyzgzyxqsrq1;

    /**
     * 发证机关1
     */
    @TableField
    private String fzjg1;

    /**
     * 从业资格类别2(字典:shzy_ldjy_cyzglb)
     */
    @TableField
    private String cyzglb2;

    /**
     * 从业资格证发证日期2
     */
    @TableField
    private Date cyzgzfzrq2;

    /**
     * 从业资格证有效起始日期2
     */
    @TableField
    private Date cyzgzyxqsrq2;

    /**
     * 从业资格证有效结束日期2
     */
    @TableField
    private Date cyzgzyxjsrq2;

    /**
     * 发证机关2
     */
    @TableField
    private String fzjg2;

    /**
     * 从业资格类别3(字典:shzy_ldjy_cyzglb)
     */
    @TableField
    private String cyzglb3;

    /**
     * 从业资格证发证日期3
     */
    @TableField
    private Date cyzgzfzrq3;

    /**
     * 从业资格证有效起始日期3
     */
    @TableField
    private Date cyzgzyxqsrq3;

    /**
     * 从业资格证有效结束日期3
     */
    @TableField
    private Date cyzgzyxjsrq3;

    /**
     * 发证机关3
     */
    @TableField
    private String fzjg3;

    /**
     * 注册(登记)记录
     */
    @TableField
    private String zcjl;

    /**
     * 继续教育记录
     */
    @TableField
    private String jxjyjl;

    /**
     * 诚信(信誉)考核记录
     */
    @TableField
    private String cxkhjl;

    /**
     * 违章和计分记录
     */
    @TableField
    private String wzjl;
}
