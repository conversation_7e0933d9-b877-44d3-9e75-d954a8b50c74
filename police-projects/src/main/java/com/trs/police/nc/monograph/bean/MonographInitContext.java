package com.trs.police.nc.monograph.bean;

import com.alibaba.fastjson.JSON;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.nc.monograph.entity.Monograph;
import com.trs.police.nc.monograph.entity.MonographTemplate;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 专刊初始化上下文
 */
@Data
@NoArgsConstructor
public class MonographInitContext implements Serializable {

    private LocalDateTime cycleStartTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 开始时间信息
     */
    private TimeInfo startTimeInfo;

    /**
     * 结束时间信息
     */
    private TimeInfo endTimeInfo;

    /**
     * 发布时间信息
     */
    private TimeInfo publishTimeInfo;


    /**
     * 专刊模板
     */
    private MonographTemplate monographTemplate;

    /**
     * 创建用户信息
     */
    private CurrentUser createUser;

    /**
     * 专刊名称
     */
    private Monograph monograph;

    /**
     * 当前期数
     */
    private Integer currentPeriod;

    public MonographInitContext(LocalDateTime cycleStartTime, LocalDateTime startTime, LocalDateTime endTime, MonographTemplate monographTemplate, CurrentUser createUser) {
        this.cycleStartTime = cycleStartTime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.startTimeInfo = new TimeInfo(startTime);
        this.endTimeInfo = new TimeInfo(endTime);
        this.monographTemplate = monographTemplate;
        this.createUser = createUser;
        this.currentPeriod = monographTemplate.getCurrentPeriod();
    }

    public MonographInitContext(LocalDateTime cycleStartTime, LocalDateTime startTime, LocalDateTime endTime, MonographTemplate monographTemplate, CurrentUser createUser, Integer currentPeriod) {
        this(cycleStartTime, startTime, endTime, monographTemplate, createUser);
        this.currentPeriod = currentPeriod;
    }

    /**
     * 转换成map用于spel解析jsonPath的map
     *
     * @return map
     */
    public Map<String, String> convertToParams() {
        String contextStr = JSON.toJSONString(this);
        Map<String, String> params = new HashMap<>();
        params.put("context", contextStr);
        return params;
    }
}
