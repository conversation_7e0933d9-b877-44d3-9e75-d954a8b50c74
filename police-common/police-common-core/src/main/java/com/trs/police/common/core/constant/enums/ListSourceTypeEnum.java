package com.trs.police.common.core.constant.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * 档案详情页列表来源类型枚举
 *
 * <AUTHOR>
 */
public enum ListSourceTypeEnum {

    /**
     * 手动录入
     */
    MANUAL(1, "manual", "手动录入"),
    /**
     * 布控
     */
    MONITOR(2, "monitor", "从布控同步："),
    /**
     * 风险
     */
    RISK(3,"risk","从风险同步：");

    @JsonValue
    @Getter
    private final Integer code;

    @Getter
    private final String name;

    @Getter
    private final String description;

    ListSourceTypeEnum(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;

    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    @JsonCreator
    public static ListSourceTypeEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (ListSourceTypeEnum typeEnum : ListSourceTypeEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

}
