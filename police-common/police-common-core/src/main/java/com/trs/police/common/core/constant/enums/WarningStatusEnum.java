package com.trs.police.common.core.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.trs.police.common.core.utils.TimeUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.Getter;

/**预警状态枚举
 *
 * <AUTHOR>
 */
public enum WarningStatusEnum {

    /**
     * 待签收
     */
    WAITING_SIGN(1, "待签收", "签收", false),
    /**
     * 已签收
     */
    SIGN_FINISH(2, "已签收", "反馈", false),
    /**
     * 已反馈
     */
    REPLY_FINISH(3, "已反馈", "处置完结", true),
    /**
     * 处置完结
     */
    PROCESS_FINISH(4, "已完结", "", true);


    @JsonValue
    @Getter
    @EnumValue
    private final Integer code;
    @Getter
    private final String name;
    @Getter
    private final String action;
    @Getter
    private final boolean finish;

    WarningStatusEnum(Integer code, String name, String action, boolean finish) {
        this.code = code;
        this.name = name;
        this.action = action;
        this.finish = finish;
    }

    /**
     * code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static WarningStatusEnum codeOf(Integer code) {
        if (Objects.nonNull(code)) {
            for (WarningStatusEnum typeEnum : WarningStatusEnum.values()) {
                if (code.equals(typeEnum.getCode())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 返回预警状态对应的过期消息类型
     *
     * @return 类型
     */
    public DelayMessageTypeEnum getOverdueMessageType() {
        switch (this) {
            //预警未签收状态，收到签收延时消息即为过期
            case WAITING_SIGN:
                return DelayMessageTypeEnum.WARNING_SIGN;
            //预警已签收（未研判）状态，收到研判延时消息即为过期
            case SIGN_FINISH:
                return DelayMessageTypeEnum.WARNING_JUDGE;
            default:
        }
        return null;
    }

    /**
     * 拼接预警过期信息
     *
     * @param status 预警状态
     * @param overdueTime 过期时间
     * @param isOverdue 是否过期
     * @return 结果
     */
    public static String getOverdueTimeString(WarningStatusEnum status, LocalDateTime overdueTime, boolean isOverdue) {
        if (status.isFinish()) {
            return "";
        }
        if (isOverdue) {
            return status.getAction() + "已逾期！";
        } else {
            final LocalDateTime now = LocalDateTime.now();
            return TimeUtil.durationToString(Duration.between(now, overdueTime)) + "后" + status.getAction() + "逾期！";
        }
    }
}
