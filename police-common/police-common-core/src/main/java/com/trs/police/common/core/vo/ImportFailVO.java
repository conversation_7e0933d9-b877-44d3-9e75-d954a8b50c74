package com.trs.police.common.core.vo;

import java.io.Serializable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 批量导入响应参数
 *
 * <AUTHOR>
 * @date 2021/7/31 23:05
 */
@Getter
@Setter
@ToString
@Builder
public class ImportFailVO implements Serializable {

    private static final long serialVersionUID = 9148127114878301032L;

    /**
     * 状态 1 - 失败
     */
    private Integer status;

    /**
     * 行号
     */
    private Integer rowIndex;

    /**
     * 描述
     */
    private String desc;

    /**
     * 身份证、群体名称等
     */
    private String key;

    /**
     * 构建失败信息
     *
     * @param status 状态
     * @param index 行号
     * @param desc 描述
     * @return 结果
     */
    public static ImportFailVO of(Integer status, Integer index, String desc) {
        return ImportFailVO.builder()
                .status(status)
                .rowIndex(index)
                .desc(desc)
                .build();
    }
}
