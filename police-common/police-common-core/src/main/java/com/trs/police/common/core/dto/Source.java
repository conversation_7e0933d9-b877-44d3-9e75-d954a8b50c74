package com.trs.police.common.core.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * 感知源信息
 *
 * <AUTHOR>
 */
@Data
public class Source implements Serializable {

    private static final long serialVersionUID = 9133396665537309740L;
    /**
     * 第三方感知源ID
     */
    private String id;

    /**
     * 编码
     */
    private String code;

    /**
     * 地址
     */
    private String address;
    /**
     * 区县
     */
    private String district;
    /**
     * 感知源名称
     */
    private String name;
    /**
     * 感知源类型
     */
    private String type;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 是否本地
     * 1 是
     * 0 否
     */
    private int isLocal;
}
