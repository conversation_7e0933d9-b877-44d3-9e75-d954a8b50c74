package com.trs.police.ds.mapper;

import com.trs.police.ds.domain.request.KeyValueTypeVO;
import com.trs.police.ds.domain.request.TimeParams;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 管控中心mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RegularMapper {

    /**
     * 统计常控
     *
     * @param type 参数
     * @param timeParams 时间
     * @return 结果
     */
    List<KeyValueTypeVO> countRegular(@Param("type") Integer type, @Param("timeParams")TimeParams timeParams);

    /**
     * 统计布控
     *
     * @param type 参数
     * @param timeParams 时间
     * @return 结果
     */
    List<KeyValueTypeVO> countMonitor(@Param("type") Integer type, @Param("timeParams")TimeParams timeParams);

    /**
     * 统计布控撤控
     *
     * @param timeParams 时间
     * @return 结果
     */
    List<KeyValueTypeVO> countMonitorRevoke(@Param("timeParams")TimeParams timeParams);
}
