package com.trs.police.message.listener.portal;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.vo.message.NoticeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;

/**
 * Description: 推送至门户消息监听器
 *
 * @author: lv.bo
 * @create: 2023-12-21 15:05
 */
@Component
@Slf4j
public class PortalMessageListener {

    @Autowired
    @Qualifier("portalKafkaTemplate")
    private KafkaTemplate<Object, Object> kafkaTemplate;

    /**
     *  云哨消息同步到统一门户的topic名字
     */
    @Value("${message.portal.topic:portal_message}")
    private String portalMessageTopic;

    /**
     * 是否同步云哨消息到统一门户，默认关闭
     */
    @Value("${message.portal.sync:false}")
    private String syncMessagePortal;

    /**
     *  推送云哨到门户的监听器
     *
     * @param event 事件
     */
    @EventListener
    @Async("pushPortalMessageExecutor")
    public void pushNoticeToPortal(PortalMessageEvent event) {
        if ("false".equalsIgnoreCase(syncMessagePortal)) {
            return;
        }
        try {
            NoticeVO noticeVO = event.getNoticeVO();
            if (noticeVO == null){
                throw new Exception("推送门户消息数据为空!");
            }
            JSONObject message = buildPortalMessage(noticeVO);
            kafkaTemplate.send(portalMessageTopic, message.toJSONString());
        } catch (Throwable throwable){
            log.error("推送门户消息出错:{}", throwable.getMessage(), throwable);
        }

    }

    /**
     *  统建统一门户消息体
     *
     * @param noticeVO noticeVO
     * @return jsonObject
     */
    private JSONObject buildPortalMessage(NoticeVO noticeVO) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("thirdPartyName", "云哨");
        jsonObject.put("messageId", noticeVO.getId());
        jsonObject.put("userId", noticeVO.getReceiver().getUserId());
        jsonObject.put("userName", noticeVO.getReceiver().getUserName());
        jsonObject.put("sourceOrganCode", noticeVO.getSender().getDeptCode());
        jsonObject.put("sourceOrganName", noticeVO.getSender().getDeptName());
        jsonObject.put("content", noticeVO.getContent());
        jsonObject.put("noticeTime", noticeVO.getTime().toEpochSecond(ZoneOffset.of("+8")));
        jsonObject.put("organCode", noticeVO.getReceiver().getDeptCode());
        jsonObject.put("organName", noticeVO.getReceiver().getDeptName());
        jsonObject.put("readStatus", noticeVO.getIsRead() != null && noticeVO.getIsRead() ? 1:0);
        jsonObject.put("remark1", noticeVO.getOperation());
        return jsonObject;
    }
}
