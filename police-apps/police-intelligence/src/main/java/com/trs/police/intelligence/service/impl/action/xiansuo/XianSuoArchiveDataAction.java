package com.trs.police.intelligence.service.impl.action.xiansuo;

import com.trs.common.base.Report;
import com.trs.common.base.Report.RESULT;
import com.trs.common.exception.ParamInvalidException;
import com.trs.common.exception.ServiceException;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.intelligence.constant.ActionEnum;
import com.trs.police.intelligence.constant.Constants;
import com.trs.police.intelligence.constant.Status;
import com.trs.police.intelligence.dto.DataActionDTO;
import com.trs.police.intelligence.entity.XianSuoBaseInfoEntity;
import com.trs.police.intelligence.service.BaseXianSuoAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.trs.common.base.PreConditionCheck.checkArgument;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/4/25 10:24
 * @since 1.0
 */
@Component
@Slf4j
public class XianSuoArchiveDataAction extends BaseXianSuoAction<DataActionDTO, Report<String>, Report<String>> {

    /**
     * actionEnum<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:09
     */
    @Override
    public ActionEnum actionEnum() {
        return ActionEnum.XIANSUO_ARCHIVE_DATA;
    }

    /**
     * mergeOneR<BR>
     *
     * @param login   参数
     * @param reports 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:36
     */
    @Override
    protected Report<String> mergeOneR(Optional<CurrentUser> login, List<Report<String>> reports) {
        return reports
                .stream()
                .filter(it -> it.getResult() == RESULT.FAIL)
                .findAny()
                .orElse(new Report<>(desc(), "成功处理"));
    }

    /**
     * check<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:43
     */
    @Override
    protected void check(Optional<CurrentUser> login, XianSuoBaseInfoEntity entity, DataActionDTO dto)
            throws ServiceException {
        super.check(login, entity, dto);
        checkArgument(
                !entity.getStatusCode().equals(Status.CAOGAOXIANG.getCode()),
                new ParamInvalidException("草稿箱的数据不能归档")
        );
    }

    /**
     * doOneAction<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 09:17
     */
    @Override
    public Report<String> doOneAction(Optional<CurrentUser> login, XianSuoBaseInfoEntity entity, DataActionDTO dto)
            throws ServiceException {
        entity.setXslx(Constants.XSLX_ARCHIVE);
        getMapper().updateById(entity);
        return new Report<>(desc(), "归档成功");
    }

    /**
     * makeLogContent<BR>
     *
     * @param login  参数
     * @param entity 参数
     * @param dto    参数
     * @param one    参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/16 17:40
     */
    @Override
    protected String makeLogContent(Optional<CurrentUser> login, XianSuoBaseInfoEntity entity, DataActionDTO dto,
                                    Report<String> one) {
        return String.format("归档线索 %s", entity.getDataTitle());
    }

}
