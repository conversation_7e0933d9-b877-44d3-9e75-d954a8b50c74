package com.trs.police.intelligence.dto;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.BaseDTO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 新增或编辑标签DTO
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/9 10:31
 * @since 1.0
 */
@Data
public class SaveOrEditLabelDTO extends BaseDTO {

    /**
     * 0或空为新建，有值为编辑
     */
    private Long dataId;

    /**
     * 线索人员标签类型
     */
    private String type;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 1-启用  0-禁用   默认启用
     */
    private Integer enable;

    public SaveOrEditLabelDTO() {
        this.enable = 1;
    }

    @Override
    protected boolean checkParams() throws ServiceException {
        PreConditionCheck.checkNotEmpty(getType(), "标签类型不能为空！");
        PreConditionCheck.checkNotEmpty(getName(), "标签名称不能为空！");
        PreConditionCheck.checkNotNull(getCategoryId(), "分类id不能为空！");
        return true;
    }
}
