package com.trs.police.intelligence.vo.third;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ProvinceClueVO
 * @Description 省厅线索对象实体
 * <AUTHOR>
 * @Date 2024/9/4 15:46
 **/
@Data
public class ProvinceClueVO implements Serializable {

    /**
     * 线索来源类型代码（字典）
     */
    private String xslylxDm;

    /**
     * 线索来源类型名称
     */
    private String xslylxMc;

    /**
     * 来源单位代码
     * 线索来源类型为外省时，该字段为空
     */
    private String lydwDm;

    /**
     * 来源单位名称
     */
    private String lydwMc;

    /**
     * 来源编号
     */
    private String lybh;

    /**
     * 来源日期，格式为时间字符串：
     * yyyy-MM-dd hh:mm:ss
     */
    private String lyrq;

    /**
     * 核查等级代码（字典），参考附
     * 录：核查等级字典
     */
    private String hcdjDm;

    /**
     * 核查等级名称
     */
    private String hcdjMc;

    /**
     * 线索方向
     */
    private String xsFx;

    /**
     * 线索编号
     */
    private String xsbh;

    /**
     * 线索标题
     */
    private String xsbt;

    /**
     * 群体类型代码（字典）,参考附录:
     * 群体类型字典
     */
    private String qtlxDm;

    /**
     * 群体类型
     */
    private String qtlxMc;

    /**
     * 群体类型细类代码（字典），参考
     * 附录：群体类型细类字典
     */
    private String qtxlDm;

    /**
     * 群体类型细类名称
     */
    private String qtxlMc;

    /**
     * 维权方式代码（字典）,参考附录:
     * 维权方式字典
     */
    private String wqfsDm;

    /**
     * 维权方式名称
     */
    private String wqfsMc;

    /**
     * 行为方式代码（字典）,参考附录:
     * 行为方式字典
     */
    private String xwfsDm;

    /**
     * 行为方式名称
     */
    private String xwfsMc;

    /**
     * 指向地代码（字典）,参考附录:指
     * 向地字典
     */
    private String zxddDm;

    /**
     * 指向地名称
     */
    private String zxddMc;

    /**
     * 指向地详细地点代码（字典）,参考
     * 附录:指向地详细地点字典
     */
    private String zxdXxddDm;

    /**
     * 指向地详细地点名称
     */
    private String zxdXxddMc;

    /**
     * 指向地具体地点
     */
    private String zxdJtdd;

    /**
     * 敏感节点代码（字典）,参考附录:
     * 敏感节点字典
     */
    private String mgjdDm;

    /**
     * 敏感节点名称
     */
    private String mgjdMc;

    /**
     * 指向时间
     */
    private String zxsj;

    /**
     * 内容
     */
    private String nr;

    /**
     * 涉及人数
     */
    private Integer sjrs;

    /**
     * 涉及对象代码（字典）,参考附录:
     * 涉及对象字典
     */
    private String sjdxDm;

    /**
     * 涉及对象名称
     */
    private String sjdxMc;

    /**
     * 拟稿人
     */
    private String ngr;

    /**
     * 签发人
     */
    private String qfr;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 采用状态代码（字典），参考附
     * 录：采用状态字典
     */
    private String cyztDm;

    /**
     * 采用状态名称
     */
    private String cyztMc;

    /**
     * 线索状态：0-删除，1-正常，2-归
     * 档
     */
    private Integer xszt;

    /**
     * 线索时间（单位状态时间）
     */
    private String xssj;

    /**
     * 上报单位代码
     */
    private String sbdwDm;

    /**
     * 上报单位名称
     */
    private String sbdwMc;

    /**
     * 合并前线索信息
     */
    private List<String> hbqXs;

}
