package com.trs.police.intelligence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.entity.CurrentUser;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * BaseEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/4/11 17:26
 * @since 1.0
 */
@Data
public abstract class BaseEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long dataId;

    @TableField
    private Long crDeptId;

    @TableField
    private String crUser;

    @TableField
    private Date crTime = new Date();

    @TableField
    private Integer isDel = 0;

    @TableField(exist = false)
    private String crUserTrueName;

    /**
     * addInfoOnCreate<BR>
     *
     * @param <T>    泛型
     * @param user   参数
     * @param entity 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/4/15 18:52
     */
    public static <T extends BaseEntity> T addInfoOnCreate(CurrentUser user, T entity) {
        Optional.ofNullable(user).ifPresent(i -> {
            entity.setCrDeptId(i.getDeptId());
            entity.setCrUser(i.getUsername());
            entity.setCrUserTrueName(StringUtils.showEmpty(i.getRealName(), i.getUsername()));
        });
        entity.setCrTime(new Date());
        entity.setIsDel(0);
        return entity;
    }
}
