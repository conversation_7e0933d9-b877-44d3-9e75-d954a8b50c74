package com.trs.police.docsonline.authapi;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserImpl implements User {

    private Long userId;

    private String userRealName;

    private Long userDeptId;

    private String userDeptName;

    public UserImpl(User user) {
        this.userId = user.getUserId();
        this.userRealName = user.getUserRealName();
        this.userDeptId = user.getUserDeptId();
        this.userDeptName = user.getUserDeptName();
    }

    @Override
    public Long getUserId() {
        return userId;
    }

    @Override
    public String getUserRealName() {
        return userRealName;
    }

    @Override
    public Long getUserDeptId() {
        return userDeptId;
    }

    @Override
    public String getUserDeptName() {
        return userDeptName;
    }
}
