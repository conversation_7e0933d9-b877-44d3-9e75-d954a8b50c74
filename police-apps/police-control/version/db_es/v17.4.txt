PUT _template/ys_control_gjxx_template
{
  "order": 0,
  "index_patterns": [
    "ys_control_gjxx-*"
  ],
  "settings": {
    "index": {
      "refresh_interval": "1s",
      "analysis": {
        "analyzer": {
          "default": {
            "type": "ik_max_word"
          },
          "search_analyzer": {
            "type": "ik_smart"
          }
        }
      },
      "number_of_shards": "3",
      "number_of_replicas": "1"
    }
  },
  "mappings": {
    "dynamic": "strict",
    "properties": {
      "create_time": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis"
      },
      "alert_type": {
        "type": "keyword"
      },
      "capture_address": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "capture_time": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis"
      },
      "date": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss||yyyy/MM/dd HH:mm:ss||strict_date_optional_time||epoch_millis"
      },
      "device_name": {
        "type": "keyword"
      },
      "device_code": {
        "type": "keyword"
      },
      "face_photo": {
        "type": "keyword"
      },
      "id_card": {
        "type": "keyword"
      },
      "latitude": {
        "type": "keyword"
      },
      "lib_name": {
        "type": "keyword"
      },
      "longitude": {
        "type": "keyword"
      },
      "photo": {
        "type": "keyword"
      },
      "similarity": {
        "type": "double"
      },
      "name": {
        "type": "keyword"
      },
      "location": {
        "type": "geo_point"
      },
      "warning_source": {
        "properties": {
          "sourceId": {
            "type": "keyword"
          },
          "sourceType": {
            "type": "long"
          },
          "datasourceType": {
            "type": "keyword"
          },
          "sourceCategory": {
            "type": "long"
          }
        }
      },
     "data_source": {
       "type": "keyword"
     },
     "related_id": {
       "type": "keyword"
     }
    }
  },
  "aliases": {}
}

PUT ys_control_gjxx-000001
{
  "aliases": {
    "ys_control_gjxx": {
      "is_write_index": true
    }
  }
}