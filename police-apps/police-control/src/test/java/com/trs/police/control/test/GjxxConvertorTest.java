package com.trs.police.control.test;

import com.trs.police.control.ControlApp;
import com.trs.police.control.constant.enums.GjxxSourceEnum;
import com.trs.police.control.converter.GjxxConverter;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEsEntity;
import com.trs.police.control.mapper.WarningFkrxyjMapper;
import com.trs.police.control.repository.FkWarningRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 轨迹信息转换测试
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(classes = ControlApp.class)
public class GjxxConvertorTest {

    @Autowired
    private GjxxConverter gjxxConverter;

    @Autowired
    private WarningFkrxyjMapper warningFkrxyjMapper;

    @Autowired
    private FkWarningRepository fkWarningRepository;

    @Test
    public void test() {
        WarningFkrxyjEntity fkrxyjEntity = warningFkrxyjMapper.selectById(1);
        WarningFkrxyjEsEntity entity = gjxxConverter.convertStrx(fkrxyjEntity, GjxxSourceEnum.ALERT_60W);
        fkWarningRepository.insert(entity);
        System.out.print("ok");
    }
}
