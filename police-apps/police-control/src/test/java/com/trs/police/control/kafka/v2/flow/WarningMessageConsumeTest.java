package com.trs.police.control.kafka.v2.flow;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.data.exception.ProcessException;
import com.trs.data.flow.DataFlowBuilder;
import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import com.trs.police.control.kafka.v2.flow.processor.ControlInfoGenerateProcessor;
import com.trs.police.control.kafka.v2.flow.processor.NoConfigWarningMessageJudgeProcessor;
import com.trs.police.control.kafka.v2.flow.processor.WarningMessageConvertProcessor;
import com.trs.police.control.kafka.v2.flow.processor.WarningMessageTransferProcessor;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.Strings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class WarningMessageConsumeTest {

    @Mock
    private WarningMessageConvertProcessor convertProcessor;

    @Mock
    private NoConfigWarningMessageJudgeProcessor judgeProcessor;

    @Mock
    private WarningMessageTransferProcessor transferProcessor;

    @Mock
    private ControlInfoGenerateProcessor generateProcessor;

    @InjectMocks
    private WarningMessageConsume warningMessageConsume;

    private final Logger log = LoggerFactory.getLogger(WarningMessageConsumeTest.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setUp() {
        // 在每个测试之前重置模拟对象，以确保测试之间的独立性
        reset(convertProcessor, judgeProcessor, transferProcessor, generateProcessor);
    }

    /**
     * 测试场景：空消息列表
     * 目的：验证当传入的消息列表为空时，方法是否能够优雅地处理而不调用任何处理器。
     */
    @Test
    public void consumePersonWarningMessage_EmptyMessages_NoProcessing() {
        List<String> messages = Collections.emptyList();

        // 调用被测方法
        warningMessageConsume.consumePersonWarningMessage(messages);

        // 验证没有任何处理器被调用
        verifyNoInteractions(convertProcessor, judgeProcessor, generateProcessor, transferProcessor);
    }

    /**
     * 测试场景：单个 JSON 消息
     * 目的：验证当传入一个 JSON 格式的人员预警消息时，所有处理器是否按顺序正确调用，并且日志记录是否正确。
     */
    @Test
    public void consumePersonWarningMessage_SingleJsonMessage_ProcessingExecuted() throws Exception {
        // 准备 JSON 消息
        String jsonMessage = getMessage();
        WarningMessageContext context = new WarningMessageContext();

        // 模拟 convertProcessor 将 JSON 字符串转换为 WarningMessageContext 对象
        when(convertProcessor.process(jsonMessage)).thenReturn(context);
        // 模拟 judgeProcessor 处理 WarningMessageContext 对象
        when(judgeProcessor.process(any(WarningMessageContext.class))).thenReturn(context);

        // 调用被测方法
        warningMessageConsume.consumePersonWarningMessage(Collections.singletonList(jsonMessage));

        // 验证 convertProcessor 和 judgeProcessor 是否被正确调用
        verify(convertProcessor, times(1)).process(jsonMessage);
        verify(judgeProcessor, times(1)).process(context);
        // 验证 generateProcessor 和 transferProcessor 是否被调用
        verify(generateProcessor, times(1)).process(anyList());
        verify(transferProcessor, times(1)).process(anyList());

        // 验证日志输出是否正确
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(log).info(captor.capture(), eq(1), anyLong());
        assertEquals("人员预警消费成功,本次消费1条数据,共耗时", captor.getValue().substring(0, 22));
    }

    /**
     * 测试场景：多个 JSON 消息
     * 目的：验证当传入多个 JSON 格式的人员预警消息时，所有处理器是否按顺序正确调用，并且日志记录是否正确。
     */
    @Test
    public void consumePersonWarningMessage_MultipleJsonMessages_ProcessingExecuted() throws Exception {
        // 准备多个 JSON 消息
        String jsonMessage1 = "{\"id\": \"1\", \"name\": \"张三\", \"type\": \"人员预警\"}";
        String jsonMessage2 = "{\"id\": \"2\", \"name\": \"李四\", \"type\": \"人员预警\"}";
        WarningMessageContext context1 = new WarningMessageContext();
        WarningMessageContext context2 = new WarningMessageContext();

        // 模拟 convertProcessor 将 JSON 字符串转换为 WarningMessageContext 对象
        when(convertProcessor.process(jsonMessage1)).thenReturn(context1);
        when(convertProcessor.process(jsonMessage2)).thenReturn(context2);
        // 模拟 judgeProcessor 处理 WarningMessageContext 对象
        when(judgeProcessor.process(context1)).thenReturn(context1);
        when(judgeProcessor.process(context2)).thenReturn(context2);

        // 调用被测方法
        warningMessageConsume.consumePersonWarningMessage(List.of(jsonMessage1, jsonMessage2));

        // 验证 convertProcessor 和 judgeProcessor 是否被正确调用
        verify(convertProcessor, times(1)).process(jsonMessage1);
        verify(convertProcessor, times(1)).process(jsonMessage2);
        verify(judgeProcessor, times(1)).process(context1);
        verify(judgeProcessor, times(1)).process(context2);
        // 验证 generateProcessor 和 transferProcessor 是否被调用
        verify(generateProcessor, times(1)).process(anyList());
        verify(transferProcessor, times(1)).process(anyList());

        // 验证日志输出是否正确
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);
        verify(log).info(captor.capture(), eq(2), anyLong());
        assertEquals("人员预警消费成功,本次消费2条数据,共耗时", captor.getValue().substring(0, 22));
    }


    @Test
    public void method() throws InterruptedException {
        String message = getMessage();
        WarningMessageContext process = process(message);
        DataFlowBuilder
                .<String, WarningMessageContext>name("消费人员预警数据任务")
                .reader((context) -> List.of(message))
                .foreach()
                //执行转换
                .processor(convertProcessor)
                //判断当前预警是否命中预警配置
                .processor(judgeProcessor)
                .endLoop()
                .processor(generateProcessor)
                .processor(transferProcessor)
                .build()
                .execute();
        log.info("context信息为：{}",process);
        Thread.sleep(50000L);
    }

    public WarningMessageContext process(String message) throws ProcessException {
        log.info("消息为: {}",message);
        if (StringUtils.isBlank(message)) {
            log.error("receive warning message blank!");
        }
        WarningMessageContext context = new WarningMessageContext();
        WarningDTO warningDTO = JSONObject.parseObject(message, WarningDTO.class);
        context.setWarningDTO(warningDTO);
        context.setMessage(message);
        String code = warningDTO.getHitInfo().getSubscribeCode();
        final String[] subscribeInfo = Strings.split(code, '_');
        context.setControlType(ControlTypeEnum.enNameOf(subscribeInfo[0]));
        if (com.trs.common.utils.StringUtils.isNumber(subscribeInfo[1])) {
            //因为关注监测的id不是Long
            context.setMonitorId(Long.parseLong(subscribeInfo[1]));
        }
        return context;
    }

    public String getMessage() {
        return "{\"dataClassification\":\"人像抓拍数据\",\"enName\":\"rxzpsj\",\"eventTime\":\"20250528104955\",\"hitInfo\":{\"customInfo\":null,\"hitModel\":null,\"hitTags\":[],\"subscribeCode\":\"monitor_1554\"},\"identifier\":\"510502199003078470\",\"identifierType\":1,\"name\":\"人像抓拍数据\",\"sensingMessage\":{\"address\":null,\"district\":null,\"id\":\"510501190000043\",\"latitude\":30.466292,\"longitude\":106.636679,\"name\":\"泸州市合江县501214义园街车站进出口人脸1_H_hjzj_QXZJ\",\"type\":\"face_camera\"},\"trackDetail\":{\"spsbgjbh\":\"51052200001190000043\",\"mzrlurl\":\"http://ip:11180/storage/v1/image/global?cluster_id=GAW_LZ_FP_1652599390&uri_base64=a3Y6Ly9rdi1zdXJ2ZWlsbGFuY2VfaW1hZ2UvNGRiNDY2NzEzNDAyMDAwMDA0MDAzNjY0YzI3Ml8xNjUyNTI1MTA0LTQ5MTQ4ZDY0LTg5NDktNDBhMy1iYjg4LTk5ODIwYmJmNmM3ZA==\",\"bhsj\":\"20240618151755\",\"data_digest\":\"new_alert0_6140457\",\"wdwgs84_sxj\":30.466292,\"data_id\":1,\"ssbhrlurl\":\"http://ip:11180/storage/v1/image/global?cluster_id=GAW_LZ_FP_1652599390&uri_base64=a3Y6Ly9rdi1zdXJ2ZWlsbGFuY2VfaW1hZ2UvNGRiNDY2NzEzNDAyMDAwMDA0MDAzNjY0YzI3MV8xNjUyNTI1MTA0LTQ5MTQ4ZDY0LTg5NDktNDBhMy1iYjg4LTk5ODIwYmJmNmM3ZA==\",\"zjhm_bzr\":\"510502199003078470\",\"jszt_bzr\":false,\"ssbhtpurl\":\"http://***********:11180/storage/v1/image/global?cluster_id=GAW_LZ_FP_1652599390&uri_base64=aHVpanU6Ly9BSUNFTEwxNDlfMTY4OTMyNjYxODoxMTE4MC9zdG9yYWdlL3YxL2ltYWdlP2NsdXN0ZXJfaWQ9QUlDRUxMMTQ5XzE2ODkzMjY2MTgmdXJpX2Jhc2U2ND1hM1k2THk5cmRpMXBiV0ZuWlMxbWJHOTNYM05qWlc1bEx6UmtZalEyTmpjeE16UTROekF3TURBek9EYzRaVGxrTURBMlpEQmZNVFk0T1RNeU5Ua3dOQzAwWXpnMFpqTTFOeTFtWXpaakxUUmtNREl0T1RFME5TMDFaRFkxWVRZek0ySmhOelk9\",\"jdwgs84_sxj\":106.636679,\"bdxsd_bzr\":91.04597556731272,\"data_source_id\":6087,\"zjlid\":\"9148d7a85e3097979f6c173916b51713\",\"spsbmc\":\"泸州市合江县501214义园街车站进出口人脸1_H_hjzj_QXZJ\"},\"userName\":\"ys\"}";
    }
}
