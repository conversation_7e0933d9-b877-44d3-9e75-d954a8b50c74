package com.trs.police.control.service.warning;

import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.control.constant.WarningFkrxyjConstant;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.web.builder.util.BeanFactoryHolder;

import java.time.LocalDateTime;

/**
 * fk消息辅助工具
 *
 * <AUTHOR>
 */
public class FkMessageHelper {

    /**
     * 构造预警实体
     *
     * @param warningDTO 预警信息
     * @return 预警实体
     */
    public static WarningFkrxyjEntity buildEntity(WarningFkrxyjDTO warningDTO) {
        WarningFkrxyjEntity entity = new WarningFkrxyjEntity();
        BeanUtil.copyPropertiesIgnoreNull(warningDTO, entity);
        // 姓名需要特殊处理
        entity.setName(warningDTO.getSuspectName());
        entity.setNation(entity.getName().contains("·")
                ? WarningFkrxyjConstant.NATION_CODE_WZ : WarningFkrxyjConstant.NATION_CODE_HZ);
        // 默认的级别和类型
        entity.setWarningLevel(MonitorLevelEnum.BLUE);
        entity.setWarningModel(WarningFkrxyjConstant.EARLY_WARNING_WITH_TRAJECTORY);
        entity.setCreateTime(LocalDateTime.now());
        Long code = BeanFactoryHolder.getEnv().getProperty("control.fkry.source.code.strx", Long.class);
        entity.setSourceType(code);
        return entity;
    }
}
