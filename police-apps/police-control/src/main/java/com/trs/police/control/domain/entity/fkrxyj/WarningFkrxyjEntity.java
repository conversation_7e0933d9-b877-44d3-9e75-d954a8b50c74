package com.trs.police.control.domain.entity.fkrxyj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 反恐人像预警(高新)
 *
 * <AUTHOR>
 * @Date 2023/12/6 11:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_warning_fkrxyj", autoResultMap = true)
public class WarningFkrxyjEntity {

    @TableId(type = IdType.AUTO)
    private Long id;
    private LocalDateTime createTime;
    private Integer age;
    private String alertType;
    private String captureAddress;
    private LocalDateTime captureTime;
    private String date;
    private String deviceCode;
    private String facePhoto;
    private String facePosition;
    @TableField(value = "`group`")
    private String group;
    private String idCard;
    private String latitude;
    private String libName;
    private String longitude;
    private String photo;
    private String similarity;
    private String suspectName;
    private String suspectPhoto;
    private String tarLibName;
    private MonitorLevelEnum warningLevel;
    /**
     * 人员姓名
     */
    private String name;

    /**
     * 模型名称：有轨迹即预警，FK首次入区，同杆徘徊，静默
     */
    private String warningModel;

    /**
     * 民族-值来源于：SELECT * FROM `t_dict` where type='nation';
     */
    private Integer nation;

    /**
     * 重点区域id
     */
    private String areaId;

    /**
     * 区域
     */
    private String location;

    /**
     * 轨迹类型
     */
    private Long sourceType;

}
