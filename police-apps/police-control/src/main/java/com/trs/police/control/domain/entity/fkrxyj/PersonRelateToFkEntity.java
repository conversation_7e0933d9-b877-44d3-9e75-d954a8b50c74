package com.trs.police.control.domain.entity.fkrxyj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 反恐人像预警(高新)
 *
 * <AUTHOR>
 * @date 2024/01/23 15:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_warning_fk_person", autoResultMap = true)
public class PersonRelateToFkEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 人员姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;


    /**
     * 人员状态：0-首次入去，1-入区三天
     */
    private Integer personStatus;

    /**
     * 首次进入时间
     */
    private LocalDateTime firstIntoTime;

    /**
     * 照片
     */
    private String photo;

    /**
     * 是否建档：0为未建档，1为已建档
     */
    private Integer onRecord;

    /**
     * 人档id
     */
    private Long personProfileId;

    /**
     * 民族-值来源于：SELECT * FROM `t_dict` where type='nation';
     */
    private Integer nation;

    /**
     * 关注监控状态：0-未关注，1-已关注
     */
    private Integer careMonitorStatus;

    /**
     * 数据来源：0-省厅库、1-基座聚档库、2-其他
     */
    private Integer dataSource;

    /**
     * 人员类别
     */
    @Deprecated
    private Integer personType;

    /**
     * 户籍地代码
     */
    private String placeCode;

    /**
     * 工作情况
     */
    private Integer workSituation;

    /**
     * 人员类别
     *
     * @deprecated 根据标签判断，不维护此字段
     */
    @Deprecated
    private Integer tenPersonType;

    /**
     * 电话
     */
    private String tel;

    /**
     * 管控级别
     */
    private Long controlLevel;

    /**
     * 最后的预警id
     */
    private Long lastWarningId;

    /**
     * 最后预警时间
     */
    private LocalDateTime lastWarningTime;

    /**
     * 标签列表[1, 2, 3]
     */
    private String labelId;

    /**
     * 责任pcs
     */
    private String zrpcs;

    /**
     * 性别 1 男 2 女
     */
    private Integer xb;

    /**
     * 摸排地址地域编码
     */
    private String mpdzCode;
}
