package com.trs.police.control.domain.entity.monitor;

import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.dto.DistrictListDto;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.core.vo.KeyValueTypeVO;
import com.trs.police.common.core.vo.permission.DeptVO;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.domain.params.MonitorAreaListParams;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_control_monitor_target_filter_params", autoResultMap = true)
public class MonitorTargetFilterParamsEntity {

    /**
     * 数据主键（Mysql 推荐使用连续自增的整数）
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 布控id
     */
    private Long monitorId;
    /**
     * 处理后的筛选条件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object filterParams;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> idNumbers;

    /**
     * 解析json
     *
     * @return {@link MonitorAreaListParams}
     */
    public MonitorAreaListParams toMonitorAreaListParams() {
        String keyValueType = "array";
        final MonitorAreaListParams listParamsRequest = new MonitorAreaListParams();
        final JsonNode jsonNode = JsonUtil.parseJsonNode(JsonUtil.toJsonString(this.getFilterParams()));
        if (Objects.isNull(jsonNode)) {
            return listParamsRequest;
        }
        final JsonNode dept = jsonNode.get("dept");
        List<KeyValueTypeVO> filterParamsList = new ArrayList<>();
        if (Objects.nonNull(dept)) {
            final PermissionService permissionService = BeanUtil.getBean(PermissionService.class);
            final List<Long> permissionDeptId = permissionService.getTreeByDataPermission().stream().map(DeptVO::getDeptId)
                .collect(Collectors.toList());
            final KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
            final List<List<Long>> collect = JsonUtil.parseArray(JsonUtil.toJsonString(dept), Long.class)
                .stream()
                .map(deptId -> {
                    final DeptDto deptDto = permissionService.getDeptById(deptId);
                    final List<Long> result = Arrays.stream(deptDto.getPath().split("-"))
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                    result.add(deptId);
                    final Long topDept = result.stream().filter(permissionDeptId::contains).findFirst().orElse(null);
                    return Objects.isNull(topDept)
                        ? new ArrayList<Long>()
                        : result.subList(result.indexOf(topDept), result.size());
                }).collect(Collectors.toList());
            keyValueTypeVO.setType(keyValueType);
            keyValueTypeVO.setKey("dept");
            keyValueTypeVO.setValue(collect);
            filterParamsList.add(keyValueTypeVO);
        }
        final JsonNode personLabel = jsonNode.get("personLabel");
        if (Objects.nonNull(personLabel)) {
            final ProfileService profileService = BeanUtil.getBean(ProfileService.class);
            final KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
            final List<List<Long>> collect = JsonUtil.parseArray(JsonUtil.toJsonString(personLabel), Long.class)
                .stream()
                .map(label -> {
                    final LabelVO labelVO = profileService.getLabel(label);
                    final List<Long> result = Arrays.stream(labelVO.getPath().split("-"))
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                    result.add(label);
                    return result;
                }).collect(Collectors.toList());
            keyValueTypeVO.setType(keyValueType);
            keyValueTypeVO.setKey("personLabel");
            keyValueTypeVO.setValue(collect);
            filterParamsList.add(keyValueTypeVO);
        }
        final JsonNode district = jsonNode.get("district");
        if (Objects.nonNull(district)) {
            final DictService dictService = BeanUtil.getBean(DictService.class);
            final KeyValueTypeVO keyValueTypeVO = new KeyValueTypeVO();
            final List<List<String>> collect = JsonUtil.parseArray(JsonUtil.toJsonString(district), String.class)
                .stream()
                .map(districtCode -> {
                    final DistrictListDto districtListDto = dictService.getDistrictByCode(districtCode);
                    final List<String> result = Arrays.stream(districtListDto.getPath().split("-"))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                    result.add(districtCode);
                    return result;
                }).collect(Collectors.toList());
            keyValueTypeVO.setType(keyValueType);
            keyValueTypeVO.setKey("district");
            keyValueTypeVO.setValue(collect);
            filterParamsList.add(keyValueTypeVO);
        }
        listParamsRequest.setFilterParams(filterParamsList);
        listParamsRequest.setPageParams(PageParams.getDefaultPageParams());
        return listParamsRequest;
    }
}
