package com.trs.police.control.handler.care.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.police.control.care.CareMonitorMessageConsumer;
import com.trs.police.control.care.CareMonitorMessageConsumerFactory;
import com.trs.police.control.domain.entity.monitor.CareMonitorEntity;
import com.trs.police.control.domain.vo.care.CareMonitorMessage;
import com.trs.police.control.handler.care.ICareMonitorHandler;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static com.trs.police.control.care.CareMonitoMsgConsumerEnum.CONTROL_COMMAND;

/**
 * @author: dingkeyu
 * @date: 2025/01/21
 * @description:
 */
@Slf4j
@Component
public class ControlCommandCareMonitorHandler implements ICareMonitorHandler {


    @Override
    public void onSubscribeSuccessful(Optional<CareMonitorEntity> newSubscribe) {

    }

    @Override
    public Long getDefaultMonitorConfigId() {
        Long id = BeanFactoryHolder.getEnv().getProperty("com.trs.police.careMonitor.config.controlCommand.monitorConfigId", Long.class);
        Objects.requireNonNull(id);
        return id;
    }

    @Override
    public void doWarningMessage(CareMonitorMessage message) {
        Optional<CareMonitorMessageConsumer> consumer = CareMonitorMessageConsumerFactory.getSpringInstance().findConsumerById(CONTROL_COMMAND);
        PreConditionCheck.checkArgument(consumer.isPresent(), "未找到相关消费者");
        consumer.get().accept(message);
    }
}
