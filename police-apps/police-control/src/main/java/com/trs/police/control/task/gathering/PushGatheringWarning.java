package com.trs.police.control.task.gathering;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.data.exception.ProcessException;
import com.trs.data.processor.IBatchDataProcessor;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.openfeign.starter.service.MessageService;
import com.trs.police.control.properties.GatheringWarningProperties;
import com.trs.police.control.service.WarningProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 推送聚集预警消息
 *
 * <AUTHOR>
 * @since 2025/4/21 22:00
 */
@Slf4j
@Component
public class PushGatheringWarning implements IBatchDataProcessor<GroupWarningDTO, Void> {

    @Resource
    private GatheringWarningProperties gatheringWarningProperties;

    @Resource
    private MessageService messageService;

    @Resource
    private WarningProcessService warningProcessService;

    @Override
    public List<Void> process(List<GroupWarningDTO> groupWarnings) throws ProcessException {
        if (groupWarnings == null || groupWarnings.isEmpty()) {
            return List.of();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        groupWarnings.forEach(groupWarningDTO -> {
            try {
                String s = objectMapper.writeValueAsString(groupWarningDTO);
                log.info("聚集预警入库，内容为：{}", s);
                warningProcessService.receiveGroupWarningMessage(groupWarningDTO);
//                KafkaMessageVO kafkaMessageVO = new KafkaMessageVO();
//                kafkaMessageVO.setData(s);
//                kafkaMessageVO.setTopic(gatheringWarningProperties.getTopic());
//                messageService.pushMessageToKafka(kafkaMessageVO);

            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        return List.of();
    }
}
