package com.trs.police.control.aspect;


import com.trs.police.common.core.constant.enums.PositionEnum;
import com.trs.police.common.core.constant.log.OperationType;
import com.trs.police.common.core.utils.AspectUtil;
import com.trs.police.common.core.utils.RemoteAddrUtil;
import com.trs.police.control.service.OperationLogService;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Configuration;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 日志aop
 *
 * <AUTHOR>
 * @since 2022/5/9 11:20
 **/
@Aspect
@Configuration
@Slf4j
public class OperationAspectT {

    @Resource
    OperationLogService operationLogService;

    /**
     * 切点
     */
    @Pointcut("@annotation(com.trs.police.control.aspect.OperationLog)")
    public void pointCut() {
        //ignore
    }

    /**
     * 运行前
     *
     * @param joinPoint    参数
     * @param operationLog 操作记录
     */
    @Before("pointCut()&& @annotation(operationLog)")
    public void before(JoinPoint joinPoint, OperationLog operationLog) {
        try {
            if (operationLog.operation().getType().equals(OperationType.MODIFY_OBJECT)) {
                final String ipAddress = getIp();
                Map<String, Object> params = AspectUtil.generateParams(joinPoint);
                Object newObj = getObject(operationLog, params);
                long id = 0L;
                if (!"".equals(operationLog.relatedId())) {
                    Object targetObject = getObjectFromParams(params, operationLog.relatedId());
                    id = Long.parseLong((Objects.requireNonNull(targetObject)).toString());
                }
                operationLogService.createOperatorLog(id, ipAddress, operationLog, newObj, null);
            }
        } catch (Exception e) {
            log.error("操作记录生成失败", e);
        }

    }

    /**
     * 获取对象
     *
     * @param operationLog 操作
     * @param params       参数
     * @return 对象
     */
    @Nullable
    private Object getObject(OperationLog operationLog, Map<String, Object> params) {
        Object newObj = null;
        if (!"".equals(operationLog.newObj())) {
            newObj = getObjectFromParams(params, operationLog.newObj());
        }
        return newObj;
    }

    /**
     * 处理
     *
     * @param joinPoint    切点
     * @param operationLog 日志
     * @param returnValue  返回值
     */
    @AfterReturning(pointcut = "pointCut()&&@annotation(operationLog)", returning = "returnValue")
    public void generateOperationLog(JoinPoint joinPoint, OperationLog operationLog, Object returnValue) {
        try {
            if (!operationLog.operation().getType().equals(OperationType.MODIFY_OBJECT)) {
                Map<String, Object> params = AspectUtil.generateParams(joinPoint);
                Object newObj = getObject(operationLog, params);
                Long id = 0L;
                if (PositionEnum.RETURN_VALUE.equals(operationLog.idPosition())) {
                    ExpressionParser parser = new SpelExpressionParser();
                    EvaluationContext context = new StandardEvaluationContext();
                    context.setVariable("returnValue", returnValue);
                    id = parser.parseExpression(operationLog.relatedId()).getValue(context, Long.class);
                } else {
                    if (!"".equals(operationLog.relatedId())) {
                        Object targetObject = getObjectFromParams(params, operationLog.relatedId());
                        id = Long.parseLong((Objects.requireNonNull(targetObject)).toString());
                    }
                }
                final String ipAddress = getIp();
                operationLogService.createOperatorLog(id, ipAddress, operationLog, newObj, returnValue);
            }
        } catch (Exception e) {
            log.error("操作记录生成失败", e);
        }
    }

    /**
     * 获取ip
     *
     * @return {@link String}
     */
    private String getIp() {
        final ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
            .getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        return RemoteAddrUtil.getRemoteAddress(request);
    }

    /**
     * 获取 对象
     *
     * @param params 参数
     * @param spel   表达式
     * @return {@link Object}
     */
    private Object getObjectFromParams(Map<String, Object> params, String spel) {
        ExpressionParser parser = new SpelExpressionParser();
        EvaluationContext context = new StandardEvaluationContext();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }
        return parser.parseExpression(spel).getValue(context, Object.class);
    }
}

