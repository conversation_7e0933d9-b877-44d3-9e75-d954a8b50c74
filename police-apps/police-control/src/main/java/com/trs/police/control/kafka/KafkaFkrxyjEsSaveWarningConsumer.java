package com.trs.police.control.kafka;

import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.control.constant.enums.GjxxSourceEnum;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.properties.KafkaFkrxyjWarningConsumerProperties;
import com.trs.police.control.service.WarningProcessService;
import com.trs.police.control.service.warning.FkMessageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 60w数据 保存到es
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaFkrxyjWarningConsumerProperties.class)
public class KafkaFkrxyjEsSaveWarningConsumer {

    private KafkaConsumer<String, String> consumer;

    private final KafkaFkrxyjWarningConsumerProperties properties;



    private final WarningProcessService warningProcessService;

    public KafkaFkrxyjEsSaveWarningConsumer(
            KafkaFkrxyjWarningConsumerProperties properties,
            WarningProcessService warningProcessService) {
        this.properties = properties;
        this.warningProcessService = warningProcessService;
        if (com.trs.common.utils.StringUtils.isEmpty(properties.getJjGroupId())) {
            log.info("fk预警未启动聚集预警消费，跳过初始化");
            return;
        }
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getBootStrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, properties.getEsSaveGroupId());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, properties.getMaxPollRecords());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, properties.getAutoOffsetReset());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);//关闭自动提交kafka事务，批量处理时控制回滚策略
        consumer = new KafkaConsumer<>(props);
        consumer.subscribe(List.of(properties.getTopic()));
    }

    /**
     * 消费人员预警信息
     */
    @Scheduled(fixedDelay = 1L)
    public void consumer() {
        if (Objects.isNull(consumer)) {
            return;
        }
        final ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(properties.getPollDuration()));
        log.info("本批次数据共消费反恐人像预警（es入库）数据：{} 条", records.count());
        List<ConsumerRecord<String, String>> recordList = new ArrayList<>(records.count());
        records.forEach(recordList::add);

        // 处理该批次消费到的数据
        List<WarningFkrxyjEntity> list = recordList.stream()
                .map(message -> {
                    if (StringUtils.isBlank(message.value())) {
                        return null;
                    }
                    return JsonUtil.parseObject(message.value(), WarningFkrxyjDTO.class);
                })
                .filter(Objects::nonNull)
                .map(FkMessageHelper::buildEntity)
                .collect(Collectors.toList());
        warningProcessService.saveStrxToEs(list, GjxxSourceEnum.ALERT_60W);
        //提交kafka事务
        consumer.commitSync();
    }
}
