package com.trs.police.control.service.approval.impl.zg;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.constant.log.OperateModule;
import com.trs.police.common.core.constant.log.Operation;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.openfeign.starter.service.ApprovalService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.common.openfeign.starter.vo.ApprovalRequest;
import com.trs.police.control.constant.enums.MonitorTypeEnum;
import com.trs.police.control.domain.vo.monitor.MonitorVO;
import com.trs.police.control.service.approval.ControlMonitorApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.trs.police.control.constant.MonitorApprovalNameConstant.*;

/**
 * 自贡环境公用的审批服务
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "control.approval.version", havingValue = "zg")
@Slf4j
public class CommonZgApprovalService implements ControlMonitorApprovalService<MonitorVO> {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ApprovalService approvalService;

    @Override
    public String approval(ApprovalRequest approvalRequest) {
        log.info("请求审批参数：{}", JSONObject.toJSONString(approvalRequest));
        String s = approvalService.startApprovalWithResult(approvalRequest);
        log.info("请求审批结果：{}", s);
        JSONObject result = JSONObject.parseObject(s);
        if (!Integer.valueOf(200).equals(result.getInteger("code"))) {
            throw new TRSException("发起审批失败，请重试");
        }
        return s;
    }

    private ApprovalRequest approval(MonitorVO vo, String configName, Operation operation) {
        Long monitorId = vo.getMonitorId();
        ApprovalActionVO actionVO = new ApprovalActionVO();
        actionVO.setService(OperateModule.MONITOR);
        actionVO.setId(monitorId);
        actionVO.setTitle(vo.getMonitorInfo().getMonitorTitle());
        actionVO.setAction(operation);
        ApprovalRequest request = new ApprovalRequest();
        request.setApprovalActionVO(actionVO);
        request.setApprovalConfigName(configName);
        request.setUser(UserDeptVO.of(AuthHelper.getCurrentUser()));
        return request;
    }

    @Override
    public ApprovalRequest buildRequest(MonitorVO t, Long monitorId, String title, MonitorTypeEnum typeEnum, Operation operation) {
        return approval(t, getMonitorApprovalType(null, null), operation);
    }


    /**
     * 获取审批配置
     *
     * @param typeEnum tp
     * @param operation op
     * @return 审批配置
     */
    public String getMonitorApprovalType(MonitorTypeEnum typeEnum, Operation operation) {
        CurrentUser currentUser = AuthHelper.getCurrentUser();
        AreaUtils.Level level = AreaUtils.level(currentUser.getDept().getDistrictCode());
        // 省级发起
        if (AreaUtils.Level.PROVINCE.equals(level)) {
            return MONITOR_INITIATE_PROVINCE;
        }
        // 市级发起
        if (AreaUtils.Level.CITY.equals(level)) {
            if (Objects.equals(1L, currentUser.getDept().getChildType())) {
                return "MONITOR_INITIATE_CITY_QZ";
            }
            return "MONITOR_INITIATE_CITY";
        }
        // 区县发起
        if (Objects.equals(1L, currentUser.getDept().getChildType())) {
            return "MONITOR_INITIATE_COUNTY_QZ";
        } else {
            return "MONITOR_INITIATE_COUNTY";
        }

    }
}
