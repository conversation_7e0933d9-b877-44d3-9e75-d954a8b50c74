package com.trs.police.control.service;

import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.WarningEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.domain.vo.care.CareMonitorInitialeVO;
import com.trs.police.control.domain.vo.warning.CareMonitorResumeResult;

import java.util.List;

/**
 * 提供给业务模块使用的关注监控服务，该服务旨在提供一个通用的能力，能够让业务系统能够快速实现自己的关注监控能力。
 * 关注监控的含义：
 * 1，能够对指定的人直接触发中台的订阅，并保存相关数据到数据库当中；
 * 2，关注监控的优先级是最低的，简单将：常控>=临控>关注监控。因此，如果一个人被关注监控过，之后设置为了临控或者常控，关注监控不会再生效，如果这个人从常控或者临控中撤控，该人会被继续关注监控；
 * 3，关注监控只有在被取消关注后，才会放弃监控；
 * 4，关注监控本质上调用的是常控，系统需要默认设置一个预警模型，关注监控也可指定自己的监控模型；
 *
 * @date 2024/4/22
 * @author: cy
 *
 */
public interface CareMonitorService {

    /**
     * initiate
     *
     * @param initialeVOList careMonitorInitialeVOS
     * @throws Exception ex
     */
    void initiate(List<CareMonitorInitialeVO> initialeVOList) throws Exception;

    /**
     * 完成预警消息的消费工作。预警消息在CareMonitorService中主要是解析后通过ICareMonitorHandler提交给各个业务模块消费
     *
     * @param controlInfo controlInfo
     * @param warningInfo warningInfo
     * @param track       track
     * @param warning     warning
     * @param dto         dto
     * @return CareMonitorResumeResult
     */
    CareMonitorResumeResult consumerMonitorMessage(ControlInfo controlInfo, ControlInfo.WarningInfo warningInfo,
                                                   GroupWarningDTO.Track track, WarningEntity warning, WarningDTO dto);

    /**
     * 手动更新无感布控
     *
     * @param ids ids
     */
    void careMonitorByHand(List<Long> ids);
}
