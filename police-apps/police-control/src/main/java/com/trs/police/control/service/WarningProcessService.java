package com.trs.police.control.service;

import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.control.constant.enums.GjxxSourceEnum;
import com.trs.police.control.domain.dto.BzWarningDTO;
import com.trs.police.control.domain.dto.ProfessionalWarningDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjDTO;
import com.trs.police.control.domain.entity.fkrxyj.WarningFkrxyjEntity;
import com.trs.police.control.domain.entity.stxf.WarningStxfDto;

import java.util.List;

/**
 * 预警消息处理
 *
 * <AUTHOR>
 */
public interface WarningProcessService {

    /**
     * 预警消息入库
     *
     * @param warningDTO 消息
     */
    void receivePersonWarningMessage(WarningDTO warningDTO);

    /**
     * 预警消息入库
     *
     * @param warningDTO 消息
     */
    void receiveGroupWarningMessage(GroupWarningDTO warningDTO);


    /**
     * 预警消息入库
     *
     * @param warningDTO 消息
     */
    void receiveXlWarningMessage(ProfessionalWarningDTO warningDTO);

    /**
     * 省厅下发预警入库
     *
     * @param warningDTO 消息
     */
    void receiveStxfWarningMessage(WarningStxfDto warningDTO);


    /**
     * 商汤人像预警入库（高新）
     *
     * @param warningDTO 消息
     * @return 预警实体
     */
    WarningFkrxyjEntity receiveFkrxyjWarningMessage(WarningFkrxyjDTO warningDTO);

    /**
     * 保存商汤rx到es
     *
     * @param entity es
     * @param source 来源
     */
    void saveStrxToEs(List<WarningFkrxyjEntity> entity, GjxxSourceEnum source);

    /**
     * 无配置轨迹类型数据入库
     *
     * @param warningDTO 消息DTO
     * @param message 消息
     */
    void receiveNoConfigWarningMessage(WarningDTO warningDTO,String message);

    /**
     * 无配置轨迹类型数据推送三方
     *
     * @param warningDTO 消息DTO
     * @param message 消息
     */
    void sendWebHookForNoConfigMessage(WarningDTO warningDTO,String message);

    /**
     * 比中轨迹入库
     *
     * @param warningDTO 消息
     */
    void receiveBzWarningMessage(BzWarningDTO warningDTO);
}
