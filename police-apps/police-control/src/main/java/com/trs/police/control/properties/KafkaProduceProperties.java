package com.trs.police.control.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 推送轨迹消息的kafka配置
 *
 * <AUTHOR>
 * @since 2024/7/23 14:33
 */
@Data
@Component
@ConfigurationProperties(prefix = KafkaProduceProperties.PREFIX)
public class KafkaProduceProperties {

    protected static final String PREFIX = "com.trs.kafka.produce";

    /**
     * kafka地址
     */
    private String bootstrapServers;

    private String securityProtocol;

    private String saslMechanism;

    private String saslKerberosServiceName;

    /**
     * krb5配置文件路径
     */
    private String krb5Path;
    /**
     * user.keytab 认证文件地址
     */
    private String keytabPath;
    /**
     * kerberos 账号
     */
    private String principal;
}