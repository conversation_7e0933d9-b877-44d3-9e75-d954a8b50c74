package com.trs.police.fight.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.fight.constant.enums.CollaborationPersonnelTypeEnum;
import com.trs.police.fight.domain.request.CollaborationListRequest;
import com.trs.police.fight.domain.vo.CollaborationVO;
import com.trs.police.fight.mapper.CollaborationMapper;
import com.trs.police.fight.service.CommonCollaborationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: dingkeyu
 * @date: 2024/03/08
 * @description: 我发起的协作
 */
@Service
public class MeStartCollaborationService implements CommonCollaborationService {

    @Resource
    private CollaborationMapper collaborationMapper;

    @Override
    public PageResult<CollaborationVO> getCollaborationList(CollaborationListRequest collaborationParams, CurrentUser currentUser) {
        PageParams pageParams = collaborationParams.getPageParams();
        Page<CollaborationVO> pageList = collaborationMapper.getMeStartCollaborations(collaborationParams, currentUser, pageParams.toPage());

        return PageResult.of(pageList.getRecords(), pageParams.getPageNumber(), pageList.getTotal(), pageParams.getPageSize());
    }

    @Override
    public Long unreadCollaborationCount(CurrentUser currentUser) {
        return collaborationMapper.meStartUnreadCount(currentUser);
    }

    @Override
    public CollaborationPersonnelTypeEnum getSearchType() {
        return CollaborationPersonnelTypeEnum.ME_START;
    }
}
