package com.trs.police.fight.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.ServiceException;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.fight.constant.CluePoolConstant;
import com.trs.police.fight.domain.dto.ClueReportDTO;
import com.trs.police.fight.domain.entity.CluePoolSyncWffzxsxxRecord;
import com.trs.police.fight.domain.entity.InternetWffzxsxxEntity;
import com.trs.police.fight.helper.CluePoolSyncOssHelper;
import com.trs.police.fight.mapper.CluePoolSyncWffzxsxxRecordMapper;
import com.trs.police.fight.mapper.InternetWffzxsxxMapper;
import com.trs.police.fight.properties.SyncInternetIllegalInfoProperties;
import com.trs.police.fight.service.impl.CluePoolServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName SyncInternetIllegalInfoToCluePoolTask
 * @Description 同步互联网违法犯罪举报信息到线索池中
 * <AUTHOR>
 * @Date 2024/7/26 15:57
 **/
@Slf4j
@Component
@ConditionalOnBean(value = SyncInternetIllegalInfoProperties.class)
public class SyncInternetIllegalInfoTask extends BaseClueSyncService<SyncInternetIllegalInfoProperties> {

    private final InternetWffzxsxxMapper wffzxsxxMapper;

    private final CluePoolSyncWffzxsxxRecordMapper recordMapper;

    private final CluePoolSyncOssHelper ossHelper;

    private static final Map<String, Long> POLICE_KIND_MAPPING = new HashMap<>(0);

    public SyncInternetIllegalInfoTask(SyncInternetIllegalInfoProperties properties, InternetWffzxsxxMapper wffzxsxxMapper, CluePoolSyncWffzxsxxRecordMapper recordMapper, CluePoolSyncOssHelper ossHelper) {
        super(properties);
        this.wffzxsxxMapper = wffzxsxxMapper;
        this.recordMapper = recordMapper;
        this.ossHelper = ossHelper;
    }

    /**
     * 获取警种类型
     *
     * @return 结果
     */
    public static Map<String, Long> getPoliceKindMapping() {
        if (POLICE_KIND_MAPPING.isEmpty()) {
            synchronized (CluePoolServiceImpl.class) {
                if (POLICE_KIND_MAPPING.isEmpty()) {
                    log.info("开始加载[police_kind]");
                    for (DictDto dto : BeanUtil.getBean(DictService.class).getDictTree(CluePoolConstant.POLICE_KIND)) {
                        POLICE_KIND_MAPPING.put(dto.getName(), dto.getCode());
                    }
                    log.info("完成加载[police_kind]");
                }
            }
        }
        return POLICE_KIND_MAPPING;
    }

    /**
     * 进行同步操作
     */
    @Scheduled(cron = "${fight.sync.internet-illegal-info.cron}")
    public void sync() {
        String msg = doSync(false);
        log.info("[{}]运行结果为：{}", desc(), msg);
    }

    @Override
    public String doSync(Boolean force) {
        final CurrentUser user = getSpecificUser();
        if (Objects.isNull(user)) {
            log.warn("未配置同步用户！");
            return "未配置同步用户！";
        }
        // 从外部PG库里面获取数据
        final List<InternetWffzxsxxEntity> list = getInternetWffzData();
        for (InternetWffzxsxxEntity entity : list) {
            try {
                final ClueReportDTO clueReportDTO = buildReportClueDto(entity);
                getCluePoolService().reportClue(clueReportDTO, user);
                // 记录同步信息记录
                this.buildAndSaveSyncRecord(user, entity);
            } catch (Exception e) {
                log.error("互联网违法犯罪信息同步-同步数据唯一ID为【{}】的信息异常！完整信息【{}】", entity.getUuid(), JSON.toJSONString(entity), e);
            }
        }
        return "同步成功！";
    }

    private List<InternetWffzxsxxEntity> getInternetWffzData() {
        // 获取上一次同步时间
        final List<CluePoolSyncWffzxsxxRecord> syncRecords = recordMapper.selectList(new QueryWrapper<CluePoolSyncWffzxsxxRecord>()
                .orderByDesc("sync_time").last("limit 1")
        );
        final List<InternetWffzxsxxEntity> list;
        if (CollectionUtils.isEmpty(syncRecords)) {
            list = wffzxsxxMapper.selectList(null);
        } else {
            final CluePoolSyncWffzxsxxRecord record = syncRecords.get(0);
            list = wffzxsxxMapper.selectList(new QueryWrapper<InternetWffzxsxxEntity>()
                    .ge("dep_firstenter_time", record.getSyncTime().format(DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS)))
            );
        }
        return list;
    }

    private CluePoolSyncWffzxsxxRecord buildAndSaveSyncRecord(CurrentUser user, InternetWffzxsxxEntity entity) {
        CluePoolSyncWffzxsxxRecord record = new CluePoolSyncWffzxsxxRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setCreateUserId(user.getId());
        record.setCreateUserName(user.getUsername());
        record.setData(JSON.toJSONString(entity));
        record.setSyncTime(LocalDateTime.now());
        recordMapper.insert(record);
        return record;
    }

    private ClueReportDTO buildReportClueDto(InternetWffzxsxxEntity entity) throws ServiceException {
        ClueReportDTO dto = new ClueReportDTO();
        if (1 == entity.getIsReal()) {
            dto.setClueTitle(String.format("%s提交的实名举报", entity.getRealName()));
        } else {
            dto.setClueTitle("匿名举报");
        }
        dto.setReportTime(LocalDateTime.parse(entity.getCreateTime(), DateTimeFormatter.ofPattern(TimeUtils.YYYYMMDD_HHMMSS)));
        dto.setClueContent(getContent(entity));
        dto.setClueType(CluePoolConstant.CLUE_TYPE_SHE_AN);
        dto.setPoliceKind(getPoliceKindCode(entity.getAlarmBell()));
        if (StringUtils.isNotEmpty(entity.getMobile()) && !"匿名".equals(entity.getMobile())) {
            dto.setRelatedPhone(entity.getMobile());
        }
        if (StringUtils.isNotEmpty(entity.getIdCard()) && !"匿名".equals(entity.getIdCard())) {
            dto.setRelatedPerson(entity.getIdCard());
        }
        // 从外部存储获取附件信息moss api
        dto.setFileList(getFileList(entity));
        return dto;
    }

    private String getFileList(InternetWffzxsxxEntity entity) throws ServiceException {
        List<String> fileNames = new ArrayList<>();
        final String videoUrl = entity.getVideo();
        if (StringUtils.isNotEmpty(videoUrl)) {
            final List<String> videoUrlList = Arrays.stream(videoUrl.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                    .filter(StringUtils::isNotEmpty)
                    .map(a -> a.replaceFirst("/upload", ""))
                    .collect(Collectors.toList());
            fileNames.addAll(videoUrlList);
        }
        final String audioUrl = entity.getAudio();
        if (StringUtils.isNotEmpty(audioUrl)) {
            final List<String> audioUrlList = Arrays.stream(audioUrl.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                    .filter(StringUtils::isNotEmpty)
                    .map(a -> a.replaceFirst("/upload", ""))
                    .collect(Collectors.toList());
            fileNames.addAll(audioUrlList);
        }
        final String imageUrl = entity.getImage();
        if (StringUtils.isNotEmpty(imageUrl)) {
            final List<String> imageUrlList = Arrays.stream(imageUrl.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                    .filter(StringUtils::isNotEmpty)
                    .map(a -> a.replaceFirst("/upload", ""))
                    .collect(Collectors.toList());
            fileNames.addAll(imageUrlList);
        }
        final String attachmentUrl = entity.getAttachment();
        if (StringUtils.isNotEmpty(attachmentUrl)) {
            final List<String> attachmentUrlList = Arrays.stream(attachmentUrl.split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                    .filter(StringUtils::isNotEmpty)
                    .map(a -> a.replaceFirst("/upload", ""))
                    .collect(Collectors.toList());
            fileNames.addAll(attachmentUrlList);
        }
        final List<FileInfoVO> files = ossHelper.getFilesFromThird(fileNames);
        return JSON.toJSONString(files);
    }

    private String getContent(InternetWffzxsxxEntity entity) {
        return "举报内容：" + entity.getContent() + "\n" +
                "举报人姓名：" + entity.getRealName() + "\n" +
                "举报人身份证号：" + entity.getIdCard() + "\n" +
                "举报人手机号：" + entity.getMobile() + "\n" +
                "举报地址：" + entity.getAddressMap() + "\n" +
                "网址：" + StringUtils.showEmpty(entity.getWeblink());
    }

    private String getPoliceKindCode(String alarmBell) {
        final Optional<String> policeKindName = Arrays.stream(StringUtils.showEmpty(alarmBell).split(StringUtils.SEPARATOR_COMMA_OR_SEMICOLON))
                .filter(StringUtils::isNotEmpty)
                .findFirst();
        if (policeKindName.isPresent()) {
            return String.valueOf(getPoliceKindMapping().getOrDefault(policeKindName.get(), 4L));
        }
        return "4";
    }

    @Override
    public String key() {
        return "syncInternet";
    }

    @Override
    public String desc() {
        return "互联网举报";
    }
}
