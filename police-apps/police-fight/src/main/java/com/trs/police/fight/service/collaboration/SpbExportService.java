package com.trs.police.fight.service.collaboration;

import com.deepoove.poi.config.ConfigureBuilder;
import com.trs.police.common.core.constant.enums.EnvEnum;
import com.trs.police.fight.constant.enums.CollaborationType;
import com.trs.police.fight.domain.vo.CollaborationVO;

import java.util.Map;

/**
 * 审批表导出服务,ce
 *
 * <AUTHOR>
 */
public interface SpbExportService {

    String SLD = "所领导"; // 审批表第1个

    String QXQZ = "区县情指"; // 审批表第二个

    String FJLD = "分局领导"; // 审批表第三个

    String ZHZ = "指挥长审核"; // 审批表第四个

    /**
     * 获取模板路径
     *
     * @param env 支持的环境
     * @param collaborationType 支持的协作
     * @param type 类型 指令单：zld 审批表：spb(默认)
     * @return 模板路径
     */
    String getTemplatePath(EnvEnum env, CollaborationType collaborationType, String type);

    /**
     * 支持的协作
     *
     * @param env 支持的环境
     * @param collaborationType 支持的协作
     * @return true/false
     */
    Boolean support(EnvEnum env, CollaborationType collaborationType);

    /**
     * 构造导出word的map
     *
     * @param vo vo
     * @param type tp
     * @param common 公共
     * @return word参数
     */
    Map<String, Object> buildMap(CollaborationVO vo, CollaborationType type, Map<String, Object> common);

    /**
     * 添加配置
     *
     * @param builder builder
     */
    void addBinder(ConfigureBuilder builder);
}
