package com.trs.police.fight.service.collaboration;

import com.trs.police.common.core.constant.enums.CollaborationStatusEnum;
import com.trs.police.common.core.entity.Collaboration;
import com.trs.police.common.openfeign.starter.vo.ApprovalActionVO;
import com.trs.police.fight.domain.dto.CollaborationDto;
import com.trs.police.fight.domain.params.collaboration.AppraiseParams;
import com.trs.police.fight.domain.params.collaboration.ApprovalParams;
import com.trs.police.fight.domain.params.collaboration.FeedbackParams;

/**
 * 协作流程相关服务
 *
 * <AUTHOR>
 */
public interface CollaborationProcessService {

    /**
     * 审批
     *
     * @param approvalParams 参数
     */
    void approval(ApprovalParams approvalParams);

    /**
     * 结束审批流程
     *
     * @param from 起始状态
     * @param collaboration 协作
     * @return 结束后的状态
     */
    CollaborationStatusEnum endReview(CollaborationStatusEnum from, Collaboration collaboration);

    /**
     * 执行协作人并结束审批流程
     *
     * @param from 起始状态
     * @param collaboration 协作
     * @param approvalActionVO 审批结果
     * @return 结束后的状态
     */
    CollaborationStatusEnum endReviewWhenApproval(CollaborationStatusEnum from, Collaboration collaboration, ApprovalActionVO approvalActionVO);

    /**
     * 反馈
     *
     * @param feedbackParams 反馈的参数
     */
    void feedback(FeedbackParams feedbackParams);

    /**
     * 退回
     *
     * @param feedbackParams 退回的参数
     */
    void refund(FeedbackParams feedbackParams);

    /**
     * 评价
     *
     * @param appraiseParams 评价参数
     */
    void appraise(AppraiseParams appraiseParams);

    /**
     * 删除协作
     *
     * @param collaborationId 协作id
     */
    void delete(Long collaborationId);

    /**
     * 撤销协作
     *
     * @param collaborationId 协作id
     */
    void revoke(Long collaborationId);

    /**
     * 催办
     *
     * @param collaborationId 协作id
     */
    void urge(Long collaborationId);

    /**
     * 送审
     *
     * @param dto 参数
     * @return new statue
     */
    CollaborationStatusEnum submitReview(CollaborationDto dto);

    /**
     * 再次提交
     *
     * @param collaborationId 参数
     */
    void submitAgain(Long collaborationId);

    /**
     * 上报
     *
     * @param collaborationId 参数
     * @param selectDeptId 选择的部门id
     */
    void report(Long collaborationId, Long selectDeptId);

    /**
     * 转发
     *
     * @param collaborationId 参数
     * @param selectDeptId 选择的部门id
     */
    void trans(Long collaborationId, Long selectDeptId);

    /**
     * 推送到第三方
     *
     * @param collaboration 协作
     */
    void pushThird(Collaboration collaboration);
}
