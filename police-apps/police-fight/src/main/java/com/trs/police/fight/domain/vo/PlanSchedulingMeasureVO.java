package com.trs.police.fight.domain.vo;


import com.trs.police.common.core.dto.DeptDto;
import lombok.Data;

import java.util.List;


/**
 * 预案调度措施关联表
 *
 * <AUTHOR>
 * @date 2024/06/18
 */
@Data
public class PlanSchedulingMeasureVO{

    /**
     * 预案调度措施类型
     */
    private String measureType;

    /**
     * 调度范围
     */
    private Double measureRange;

    /**
     * 参与部门信息
     */
    private List<DeptDto> deptList;

    /**
     * 核心圈半径
     */
    private Double coreRadius;

    /**
     * 内圈半径
     */
    private Double innerRadius;

    /**
     * 外圈半径
     */
    private Double outerRadius;

}
