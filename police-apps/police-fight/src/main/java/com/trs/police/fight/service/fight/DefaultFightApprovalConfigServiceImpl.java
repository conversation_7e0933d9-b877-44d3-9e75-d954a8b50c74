package com.trs.police.fight.service.fight;

import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.FightComposite;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.fight.service.FightApprovalConfigService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import static com.trs.police.fight.constant.FightOperationConstant.ADD_COMPOSITE_APPROVAL;
import static com.trs.police.fight.constant.FightOperationConstant.ADD_COMPOSITE_PERSON_APPROVAL;

/**
 * 默认作战审批配置服务
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ys.fight.fight.approval.version", havingValue = "v1", matchIfMissing = true)
public class DefaultFightApprovalConfigServiceImpl implements FightApprovalConfigService {

    @Autowired
    private PermissionService permissionService;

    @Override
    public String getCreateCompositeConfig(FightComposite fightComposite) {
        Boolean enable = BeanFactoryHolder.getEnv().getProperty("composite.approvalPoliceStation", Boolean.class, false);
        return Boolean.TRUE.equals(enable)
                ? ADD_COMPOSITE_APPROVAL.get(isPoliceStation())
                : ADD_COMPOSITE_APPROVAL.get(Boolean.FALSE);
    }

    @Override
    public String getAddCompositePersonConfig() {
        Boolean enable = BeanFactoryHolder.getEnv().getProperty("composite.approvalPoliceStation", Boolean.class, false);
        return Boolean.TRUE.equals(enable)
                ? ADD_COMPOSITE_PERSON_APPROVAL.get(isPoliceStation())
                : ADD_COMPOSITE_PERSON_APPROVAL.get(Boolean.FALSE);
    }

    /**
     * 是否是派出所
     *
     * @return true 是 false 不是
     */
    private Boolean isPoliceStation() {
        CurrentUser user = AuthHelper.getCurrentUser();
        AreaUtils.Level level = AreaUtils.level(user.getDept().getDistrictCode());
        if (!AreaUtils.Level.COUNTY.equals(level)) {
            return false;
        }
        Long targetType = 3L;
        DeptDto deptById = user.getDept();
        // 如果地域是区县 还得判断上级部门是不是派出所
        while (AreaUtils.Level.COUNTY.equals(AreaUtils.level(deptById.getDistrictCode()))) {
            if (targetType.equals(deptById.getType())) {
                return true;
            }
            deptById = permissionService.getDeptById(deptById.getPid());
        }
        return false;
    }
}
