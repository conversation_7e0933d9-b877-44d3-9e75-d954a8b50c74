package com.trs.police.comparison.data.extract.schedule;

import com.trs.police.comparison.data.extract.wrapper.StringRedisCommandsWrapper;
import io.lettuce.core.api.sync.RedisHashCommands;
import io.lettuce.core.api.sync.RedisKeyCommands;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时将redis中的监控数据写入数据库
 *
 * <AUTHOR>
 * @since 2024/7/11 10:11
 */

@Slf4j
@Component
public class MonitorInfoWriteSchedule {

    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    private final RedisHashCommands<String, String> redisHashCommands;

    private final RedisKeyCommands<String, String> redisKeyCommands;

    public MonitorInfoWriteSchedule(@Autowired DataSource dataSource,
                                    @Autowired StringRedisCommandsWrapper redisCommandsWrapper) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        this.namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        this.redisKeyCommands = redisCommandsWrapper.getRedisKeyCommands();
        this.redisHashCommands = redisCommandsWrapper.getRedisHashCommands();
    }

    /**
     * 每小时保存一次redis中的监控数据
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void scheduleExecute() {
        //如果开始使用新key 将redis中统计数据写入mysql
        persistenceToDb();
    }

    /**
     * 将redis中监控数据写入数据库
     */
    public void persistenceToDb() {
        log.info("redis中监控数据开始写入数据库");
        try {
            //获取redis中记录的接收数据量
            List<String> receiveKeys = redisKeyCommands.keys("com:trs:police:comparison:receive*");
            List<MonitorInfo> monitorDataList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(receiveKeys)) {
                for (String key : receiveKeys) {
                    Map<String, String> receive = redisHashCommands.hgetall(key);
                    for (String enName : receive.keySet()) {
                        MonitorInfo monitorInfo = new MonitorInfo();
                        monitorDataList.add(monitorInfo);
                        String dataHour = key.split("_")[1];
                        monitorInfo.setDateHour(LocalDateTime.parse(dataHour, DateTimeFormatter.ofPattern("yyyyMMddHH")));
                        monitorInfo.setDataSourceEnName(enName);
                        monitorInfo.setMonitorType("receive");
                        monitorInfo.setNum(Long.parseLong(receive.get(enName)));
                    }
                }
            }
            //获取redis中记录的推送数据量
            List<String> pushKeys = redisKeyCommands.keys("com:trs:police:comparison:push*");
            if (!CollectionUtils.isEmpty(pushKeys)) {
                for (String key : pushKeys) {
                    Map<String, String> push = redisHashCommands.hgetall(key);
                    for (String enName : push.keySet()) {
                        MonitorInfo monitorInfo = new MonitorInfo();
                        monitorDataList.add(monitorInfo);
                        String dataHour = key.split("_")[1];
                        monitorInfo.setDateHour(LocalDateTime.parse(dataHour, DateTimeFormatter.ofPattern("yyyyMMddHH")));
                        monitorInfo.setDataSourceEnName(enName);
                        monitorInfo.setMonitorType("push");
                        monitorInfo.setNum(Long.parseLong(push.get(enName)));
                    }
                }
            }

            //接收与推送日志保存到数据库
            if (!monitorDataList.isEmpty()) {
                String sql = "insert into t_monitor_info(date_hour, data_source_en_name, num, monitor_type) " +
                        " values(:date_hour, :data_source_en_name, :num, :monitor_type) " +
                        " ON DUPLICATE KEY UPDATE num = :num";

                ArrayList<MapSqlParameterSource> parameterList = new ArrayList<>();
                for (MonitorInfo monitorInfo : monitorDataList) {
                    if (monitorInfo.getDateHour() == null || monitorInfo.getDataSourceEnName() == null || monitorInfo.getMonitorType() == null) {
                        continue;
                    }
                    MapSqlParameterSource insertParameter = new MapSqlParameterSource();
                    HashMap<String, Object> parameter = new HashMap<>(10);
                    insertParameter.addValues(parameter);
                    parameter.put(MonitorInfo.DATE_HOUR_FIELD, monitorInfo.getDateHour());
                    parameter.put(MonitorInfo.DATA_SOURCE_EN_NAME_FIELD, monitorInfo.dataSourceEnName);
                    parameter.put(MonitorInfo.MONITOR_TYPE_FIELD, monitorInfo.monitorType);
                    parameter.put(MonitorInfo.NUM_FIELD, monitorInfo.getNum());
                    parameterList.add(insertParameter);
                }
                namedParameterJdbcTemplate.batchUpdate(sql, parameterList.toArray(new MapSqlParameterSource[0]));
            }
        } catch (Exception e) {
            log.error("redis中监控数据写入数据库出错", e);
        }
        log.info("redis中监控数据写入数据库完成");
    }

    @Data
    static class MonitorInfo {

        private static final String DATE_HOUR_FIELD = "date_hour";

        private static final String DATA_SOURCE_EN_NAME_FIELD = "data_source_en_name";

        private static final String MONITOR_TYPE_FIELD = "monitor_type";

        private static final String NUM_FIELD = "num";

        private LocalDateTime dateHour;

        private Long num;

        private String dataSourceEnName;

        private String monitorType;
    }


}
