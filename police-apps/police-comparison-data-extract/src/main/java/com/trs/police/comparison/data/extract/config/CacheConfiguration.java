package com.trs.police.comparison.data.extract.config;

import com.trs.police.comparison.data.extract.config.properties.EhcacheProperties;
import lombok.extern.slf4j.Slf4j;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;


/**
 * 配置ehcache缓存对象
 *
 * <AUTHOR>
 * @since 2024/6/27 16:01
 */
@Slf4j
@Configuration
public class CacheConfiguration {

    @Resource
    private EhcacheProperties properties;

    /**
     * 获取ehcache对象
     *
     * @return ehcache对象
     */
    @Bean
    public Cache cache(){
        Cache cache = null;
        try {
            net.sf.ehcache.config.Configuration config = new net.sf.ehcache.config.Configuration();
            net.sf.ehcache.config.CacheConfiguration cacheConfiguration = new net.sf.ehcache.config.CacheConfiguration();
            cacheConfiguration.setMaxEntriesLocalHeap(properties.getMaxElementsInMemory());
            cacheConfiguration.setName(properties.getCacheName());
            cacheConfiguration.eternal(properties.isEternal());
            cacheConfiguration.setMaxEntriesLocalDisk(properties.getMaxElementsOnDisk());
            cacheConfiguration.setMemoryStoreEvictionPolicy("LRU");
            config.addCache(cacheConfiguration);
            config.setName(properties.getCacheName());
            CacheManager cacheManager = CacheManager.create(config);
            cache = cacheManager.getCache(properties.getCacheName());
        } catch (Exception e){
            log.error("获取ehcache缓存对象出错", e);
        }
        if (cache == null) {
            log.error("获取ehcache缓存对象出错, 获取到的缓存对象为空");
        }
        return cache;
    }
}
