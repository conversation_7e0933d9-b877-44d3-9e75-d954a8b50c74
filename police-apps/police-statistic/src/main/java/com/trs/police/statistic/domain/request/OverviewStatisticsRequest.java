package com.trs.police.statistic.domain.request;

import com.trs.police.common.core.params.TimeParams;
import com.trs.police.common.core.vo.CodeNameVO;
import com.trs.police.statistic.domain.enums.SecuritySituationOverviewCaseType;
import com.trs.police.statistic.domain.enums.SecuritySituationOverviewCategory;
import com.trs.police.statistic.domain.enums.TimeRangeTypeEnum;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/7/18 23:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverviewStatisticsRequest {

    /**
     * 时间参数
     */
    private TimeParams timeParams;
    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 时间范围类型：当前、环比、同比
     */
    TimeRangeTypeEnum rangeType = TimeRangeTypeEnum.CURRENT;

    /**
     * 统计类别 1. 警情 2.立案 3.破案 4.抓获
     */
    SecuritySituationOverviewCategory category;

    /**
     * 案件类别
     */
    SecuritySituationOverviewCaseType caseType;

    /**
     * 子部门
     */
    List<CodeNameVO> childDepts;
}
