package com.trs.police.ulportal.service.ys;

import com.grt.condify.exception.CondifyException;
import com.trs.police.ulportal.domain.dto.ys.MonitorWarningModelListDto;
import com.trs.police.ulportal.domain.entity.ys.MonitorWarningModelEntity;
import com.trs.police.ulportal.domain.vo.ys.MonitorWarningModelStatisticVO;
import com.trs.police.ulportal.domain.vo.ys.MonitorWarningModelVO;
import com.trs.police.ulportal.service.BaseService;
import com.trs.web.builder.base.RestfulResultsV2;

/**
 * 模型服务
 */
public interface MonitorWarningModelService extends BaseService<MonitorWarningModelEntity, MonitorWarningModelVO> {

    /**
     * 获取模型统计信息
     *
     * @return 返回值
     */
    RestfulResultsV2<MonitorWarningModelStatisticVO> statisticInfo() throws CondifyException;

    /**
     * 获取数据列表
     *
     * @param dto dto
     * @return 返回值
     */
    RestfulResultsV2<MonitorWarningModelVO> getMonitorWarningModelList(MonitorWarningModelListDto dto) throws CondifyException;

    /**
     * 模型各警种贡献度排行榜
     *
     * @param count 排行榜数量
     * @return 排行榜
     * @throws CondifyException 异常
     */
    RestfulResultsV2 getPoliceCategoryRank(Integer count) throws CondifyException;

    /**
     * 根据模型id获取模型详情
     *
     * @param id 模型id
     * @return 模型信息
     */
    RestfulResultsV2 getModelInfoById(Long id);

    /**
     * 根据模型id获取模型详情（包含线索信息）
     *
     * @param dto dto
     * @return 线索信息
     */
    RestfulResultsV2 getCluelInfoByModelId(MonitorWarningModelListDto dto);
}
