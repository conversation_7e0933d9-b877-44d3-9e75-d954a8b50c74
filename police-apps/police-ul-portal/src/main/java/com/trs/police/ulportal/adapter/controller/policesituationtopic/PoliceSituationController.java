package com.trs.police.ulportal.adapter.controller.policesituationtopic;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.domain.dto.policesituationtopic.JqProcessListDTO;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceAramPeopleDTO;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceSituationListDTO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.*;
import com.trs.police.ulportal.service.policesituationtopic.PoliceSituationService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-05-14 19:00
 */
@RestController
@Api(value = "警情", tags = "警情")
@RequiredArgsConstructor
@RequestMapping("/ps")
@Slf4j
public class PoliceSituationController {

    private final PoliceSituationService policeSituationService;

    /**
     * 获取警情类别树，异步层级加载
     *
     * @param parentCode 父节点code 顶级类别时传空串
     * @return 获取警情类别树
     */
    @GetMapping("/queryPsType")
    @ApiOperation(value = "获取警情类别树，异步层级加载", notes = "获取警情类别树，异步层级加载")
    public RestfulResultsV2<PoliceSituationTypeVo> queryPsType(String parentCode) {
        return policeSituationService.queryPsType(parentCode);
    }

    /**
     * 获取全部警情类别
     *
     * @return 获取全部警情类别
     */
    @GetMapping("/queryAllPsType")
    public RestfulResultsV2<PoliceSituationTypeVo> queryAllPsType() {
        return policeSituationService.queryAllPsType();
    }

    /**
     * 查询接警警情列表
     *
     * @param dto dto
     * @return 警情列表
     */
    @PostMapping("/queryJjPsList")
    @ApiOperation(value = "查询接警警情列表", notes = "查询接警警情列表")
    public RestfulResultsV2<JqDetailVo> queryJjPsList(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.queryJjPsList(dto);
    }

    /**
     * 查询接警警情列表
     *
     * @param dto dto
     * @return 对话接口
     * @throws PortalException exception
     */
    @PostMapping("/queryJjPsConversation")
    @ApiOperation(value = "查询接警警情对话", notes = "查询接警警情对话")
    public JSONObject queryJjPsConversation(@RequestBody @Validated PoliceSituationListDTO dto) throws PortalException {
        return policeSituationService.queryJjPsConversation(dto);
    }

    /**
     * 查询出警警情列表
     *
     * @param dto dto
     * @return 警情列表
     */
    @PostMapping("/queryCjPsList")
    @ApiOperation(value = "查询出警警情列表", notes = "查询出警警情列表")
    public RestfulResultsV2<JqDetailVo> queryCjPsList(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.queryCjPsList(dto);
    }

    /**
     * 查询出警处置流程
     *
     * @param dto dto
     * @return 警情列表
     */
    @PostMapping("/queryJqProcessList")
    @ApiOperation(value = "查询出警处置流程", notes = "查询出警处置流程")
    public RestfulResultsV2<JqDetailVo> queryJqProcessList(@RequestBody @Validated JqProcessListDTO dto) {
        return policeSituationService.queryJqProcessList(dto);
    }

    /**
     * 查询出警警情对话
     *
     * @param dto dto
     * @return 对话接口
     * @throws PortalException exception
     */
    @PostMapping("/queryCjPsConversation")
    @ApiOperation(value = "查询出警警情对话", notes = "查询出警警情对话")
    public JSONObject queryCjPsConversation(@RequestBody @Validated PoliceSituationListDTO dto) throws PortalException {
        return policeSituationService.queryCjPsConversation(dto);
    }


    /**
     * 查询警情分析空间时间分析列表
     *
     * @param dto dto
     * @return 空间时间列表
     */
    @PostMapping("/queryJqSpaceList")
    @ApiOperation(value = "查询警情分析空间时间分析列表", notes = "查询警情分析空间时间分析列表")
    public RestfulResultsV2<Map<String, List<JqSpaceAnalysisVO>>> queryJqSpaceList(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.queryJqSpaceList(dto);
    }

    /**
     * 查询警情时间范围分析
     *
     * @param dto dto
     * @return 时间范围分析
     */
    @PostMapping("/getJqTimeRange")
    @ApiOperation(value = "查询警情时间范围分析", notes = "查询警情时间范围分析")
    public RestfulResultsV2<JqTimeRangeVO> getJqTimeRange(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.getJqTimeRange(dto);
    }

    /**
     * 查询警情按时间段分析
     *
     * @param dto dto
     * @return 时间段分析
     */
    @PostMapping("/getJqHourRange")
    @ApiOperation(value = "查询警情按时间段分析", notes = "查询警情按时间段分析")
    public RestfulResultsV2<JqTimeRangeVO> getJqHourRange(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.getJqHourRange(dto);
    }

    /**
     * 获取警情详情
     *
     * @param dto dto
     * @return 警情列表
     */
    @PostMapping("/queryJqDetailList")
    @ApiOperation(value = "获取警情详情", notes = "获取警情详情")
    public RestfulResultsV2<JqDetailVo> queryJqDetailList(@RequestBody @Validated JqProcessListDTO dto) {
        return policeSituationService.queryJqDetailList(dto);
    }

    /**
     * 获取报警人员分析人数
     *
     * @param dto dto
     * @return 报警人员分析人数
     */
    @PostMapping("/getJqAlarmPeopleCount")
    @ApiOperation(value = "获取报警人员分析人数", notes = "获取报警人员分析人数")
    public RestfulResultsV2<JqAlarmPeopleCountVO> getJqAlarmPeopleCount(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.queryJqAlarmPeopleCount(dto);
    }

    /**
     * 获取报警人员信息列表
     *
     * @param dto dto
     * @return 报警人员信息列表
     */
    @PostMapping("/getJqAlarmPeopleList")
    @ApiOperation(value = "获取报警人员信息列表", notes = "获取报警人员信息列表")
    public RestfulResultsV2<JqAlarmPeopleListVO> getJqAlarmPeopleList(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.queryJqAlarmPeopleList(dto);
    }

    /**
     * 获取警情词云
     *
     * @param dto dto
     * @return 警情词云
     */
    @PostMapping("/getJqWordCloud")
    @ApiOperation(value = "获取警情词云", notes = "获取警情词云")
    public RestfulResultsV2<JqWordCloudVO> getJqWordCloud(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.queryJqWordCloud(dto);
    }

    /**
     * 获取12345热线列表
     *
     * @param dto dto
     * @return 12345热线列表
     */
    @PostMapping("/getsHotLineList")
    @ApiOperation(value = "获取12345热线列表", notes = "获取12345热线列表")
    public RestfulResultsV2<JqHotLineListVO> getsHotLineList(@RequestBody @Validated PoliceSituationListDTO dto) {
        return policeSituationService.getsHotLineList(dto);
    }

    /**
     * 获取报警人员详情
     *
     * @param dto dto
     * @return 报警人员详情
     */
    @PostMapping("/getAlarmPeopleDetail")
    @ApiOperation(value = "获取报警人员详情", notes = "获取报警人员详情")
    public RestfulResultsV2<JqAlarmPeopleVO> getAlarmPeopleDetail(@RequestBody @Validated PoliceAramPeopleDTO dto) {
        return policeSituationService.getAlarmPeopleDetail(dto);
    }
}
