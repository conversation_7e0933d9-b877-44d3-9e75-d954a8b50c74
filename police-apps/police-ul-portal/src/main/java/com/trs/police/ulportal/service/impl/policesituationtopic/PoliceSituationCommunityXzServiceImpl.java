package com.trs.police.ulportal.service.impl.policesituationtopic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.police.ulportal.common.config.JqfxDeptConfig;
import com.trs.police.ulportal.common.util.TimeUtil;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceCommunityXzCountDTO;
import com.trs.police.ulportal.domain.entity.policesituationtopic.PoliceSituationCommunityXzEntity;
import com.trs.police.ulportal.domain.vo.policesituationtopic.*;
import com.trs.police.ulportal.mapper.policesituationtopic.PoliceSituationCommunityXzMapper;
import com.trs.police.ulportal.service.policesituationtopic.PoliceSituationCommunityXzService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.vavr.Tuple3;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2024-06-18 15:45
 */
@Service
@RequiredArgsConstructor
public class PoliceSituationCommunityXzServiceImpl extends ServiceImpl<PoliceSituationCommunityXzMapper, PoliceSituationCommunityXzEntity> implements PoliceSituationCommunityXzService {

    private final PoliceSituationCommunityXzMapper policeSituationCommunityXzMapper;

    private final JqfxDeptConfig jqfxDeptConfig;

    @Override
    public RestfulResultsV2<PoliceSituationCommunityXzVO> queryForPage(IPage<PoliceSituationCommunityXzEntity> page, QueryWrapper<PoliceSituationCommunityXzEntity> queryWrapper) {
        return null;
    }

    @Override
    public RestfulResultsV2<CommonResultVo> getDeptPoliceCount(PoliceCommunityXzCountDTO dto) throws Exception {
        List<String> codes = jqfxDeptConfig.getCodeList();
        Tuple3<PoliceCommunityXzCountDTO, PoliceCommunityXzCountDTO, PoliceCommunityXzCountDTO> dtoTuple3 = TimeUtil.getJqSearchDto(dto);
        CommonResultVo vo = new CommonResultVo();
        List<CommonPoliceDeptCountVO> currData = policeSituationCommunityXzMapper.getDeptPoliceCount(dto, codes);
        vo.setCurData(currData);
        List<CommonPoliceDeptCountVO> lastMonthData = policeSituationCommunityXzMapper.getDeptPoliceCount(dtoTuple3._2, codes);
        vo.setLastMonthData(lastMonthData);
        List<CommonPoliceDeptCountVO> lastYearData = policeSituationCommunityXzMapper.getDeptPoliceCount(dtoTuple3._3, codes);
        vo.setLastYearData(lastYearData);
        return RestfulResultsV2.ok(vo);
    }

    @Override
    public RestfulResultsV2<CommonResultVo> getCommonPoliceCount(PoliceCommunityXzCountDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getJqlbdm())){
            return getTotalCategoryCount(dto);
        }
        List<String> codes = jqfxDeptConfig.getCodeList();
        Tuple3<PoliceCommunityXzCountDTO, PoliceCommunityXzCountDTO, PoliceCommunityXzCountDTO> dtoTuple3 = TimeUtil.getJqSearchDto(dto);
        CommonResultVo vo = new CommonResultVo();
        List<CommonPoliceTypeCountVO> currData = policeSituationCommunityXzMapper.getChildCategoryCount(dto, codes);
        sumSubListCount(currData, dto, codes);
        vo.setCurData(currData);
        List<CommonPoliceTypeCountVO> lastMonthData = policeSituationCommunityXzMapper.getChildCategoryCount(dtoTuple3._2, codes);
        sumSubListCount(lastMonthData, dtoTuple3._2, codes);
        vo.setLastMonthData(lastMonthData);
        List<CommonPoliceTypeCountVO> lastYearData = policeSituationCommunityXzMapper.getChildCategoryCount(dtoTuple3._3, codes);
        sumSubListCount(lastYearData, dtoTuple3._3, codes);
        vo.setLastYearData(lastYearData);
        return RestfulResultsV2.ok(vo);
    }

    /**
     *  统计之类的警情数量
     *
     * @param currList 当前统计集合
     * @param dto dto
     * @param codeList codeList
     */
    private void sumSubListCount(List<CommonPoliceTypeCountVO> currList, PoliceCommunityXzCountDTO dto, List<String> codeList){
        if (CollectionUtils.isEmpty(currList)){
            return;
        }
        List<String> lbdmList = currList.stream().map(CommonPoliceTypeCountVO::getJqlbdm).collect(Collectors.toList());
        List<CommonPoliceTypeCountVO> parentCodeList = policeSituationCommunityXzMapper.getParentCodeCountList(dto, codeList, lbdmList);
        Map<String, Long> voMap = parentCodeList.stream().collect(Collectors.toMap(CommonPoliceTypeCountVO::getJqlbdm, CommonPoliceTypeCountVO::getCount));
        currList.forEach(e ->{
            Long count = voMap.get(e.getJqlbdm());
            e.setCount(e.getCount() + Optional.ofNullable(count).orElse(0L));
        });
        currList.sort((o1, o2) -> o2.getCount().intValue() - o1.getCount().intValue());
    }

    /**
     *  获取一级的警情类别统计
     *
     * @param dto dto
     * @return 统计结果
     * @throws Exception 异常信息
     */
    private RestfulResultsV2<CommonResultVo> getTotalCategoryCount(PoliceCommunityXzCountDTO dto) throws Exception {
        List<String> codes = jqfxDeptConfig.getCodeList();
        Tuple3<PoliceCommunityXzCountDTO, PoliceCommunityXzCountDTO, PoliceCommunityXzCountDTO> dtoTuple3 = TimeUtil.getJqSearchDto(dto);
        CommonResultVo vo = new CommonResultVo();
        List<CommonPoliceTypeCountVO> currData = policeSituationCommunityXzMapper.getTotalCategoryCount(dto, codes);
        vo.setCurData(currData);
        List<CommonPoliceTypeCountVO> lastMonthData = policeSituationCommunityXzMapper.getTotalCategoryCount(dtoTuple3._2, codes);
        vo.setLastMonthData(lastMonthData);
        List<CommonPoliceTypeCountVO> lastYearData = policeSituationCommunityXzMapper.getTotalCategoryCount(dtoTuple3._3, codes);
        vo.setLastYearData(lastYearData);
        return RestfulResultsV2.ok(vo);
    }
}
