package com.trs.police.ulportal.service;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-20 14:26
 */
public interface SyncUserService {

    /**
     *  同步第三方用户信息
     *
     * @throws Exception 异常
     */
    void syncThirdUserInfo() throws Exception;

    /**
     *  同步第三方用户信息
     *
     * @throws Exception 异常
     */
    void syncUser() throws Exception;

    /**
     *  同步第三方部门信息
     *
     * @throws Exception 异常
     */
    void syncDept() throws Exception;

    /**
     *  同步第三方用户部门关系
     *
     * @throws Exception 异常
     */
    void syncUserDept() throws Exception;
}
