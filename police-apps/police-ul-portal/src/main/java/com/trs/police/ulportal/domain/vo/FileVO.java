package com.trs.police.ulportal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2023-12-13 16:16
 */
@Data
@Builder
public class FileVO implements Serializable {
    private static final long serialVersionUID = -4326478525416677075L;

    /**
     *  文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     *  文件原名
     */
    @ApiModelProperty("文件原名")
    private String fileDesc;

    /**
     *  可访问的url
     */
    @ApiModelProperty("可访问的url")
    private String fileUrl;
}
