package com.trs.police.ulportal.service.impl.policesituationtopic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.grt.condify.parser.MybatisSearchParser;
import com.trs.police.ulportal.common.util.HierarchicalUtil;
import com.trs.police.ulportal.common.util.ResultHelper;
import com.trs.police.ulportal.domain.dto.policesituationtopic.ImportantPsDTO;
import com.trs.police.ulportal.domain.dto.policesituationtopic.PoliceSituationImportantListDTO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.CollegeVO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.FeatureVO;
import com.trs.police.ulportal.domain.vo.policesituationtopic.JqDetailVo;
import com.trs.police.ulportal.domain.vo.policesituationtopic.SchoolVO;
import com.trs.police.ulportal.mapper.policesituationtopic.PoliceSituationImportantMapper;
import com.trs.police.ulportal.service.policesituationtopic.PoliceSituationImportantService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-11-04 10:02
 */
@Service
@RequiredArgsConstructor
public class PoliceSituationImportantServiceImpl implements PoliceSituationImportantService {

    private final PoliceSituationImportantMapper policeSituationImportantMapper;
    @Override
    public RestfulResultsV2<JqDetailVo> queryWcnJqList(PoliceSituationImportantListDTO dto) {
        //处理特殊参数
        if (dto.getLbCodes() != null) {
            List<String> yxLbCodes = dto.getLbCodes().stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
            dto.setLbCodes(yxLbCodes);
        }

        IPage<JqDetailVo> page = MybatisSearchParser.buildPage(dto);
        Page<JqDetailVo> wcnJqList = policeSituationImportantMapper.queryWcnJqList(page, dto);
        return ResultHelper.getIPageConverter().convert(wcnJqList);
    }

    @Override
    public RestfulResultsV2<SchoolVO> getSchoolList(ImportantPsDTO dto) {
        IPage<SchoolVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<SchoolVO> schoolVoPage = policeSituationImportantMapper.getSchoolList(page, dto);
        return ResultHelper.getIPageConverter().convert(schoolVoPage);
    }

    @Override
    public RestfulResultsV2<FeatureVO> getRiskFeatureList(ImportantPsDTO dto) {
        IPage<FeatureVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<FeatureVO> featureVoPage = policeSituationImportantMapper.getRiskFeatureList(page, dto);
        return ResultHelper.getIPageConverter().convert(featureVoPage);
    }

    @Override
    public RestfulResultsV2<JqDetailVo> queryGxJqList(PoliceSituationImportantListDTO dto) {
        //处理特殊参数
        if (dto.getLbCodes() != null) {
            List<String> yxLbCodes = dto.getLbCodes().stream()
                    .map(HierarchicalUtil::getEffectiveCode)
                    .collect(Collectors.toList());
            dto.setLbCodes(yxLbCodes);
        }
        IPage<JqDetailVo> page = MybatisSearchParser.buildPage(dto);
        Page<JqDetailVo> gxJqList = policeSituationImportantMapper.queryGxJqList(page, dto);
        return ResultHelper.getIPageConverter().convert(gxJqList);
    }

    @Override
    public RestfulResultsV2<CollegeVO> getCollegeList(ImportantPsDTO dto) {
        IPage<CollegeVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<CollegeVO> collegeVoPage = policeSituationImportantMapper.getCollegeList(page, dto);
        return ResultHelper.getIPageConverter().convert(collegeVoPage);
    }
}
