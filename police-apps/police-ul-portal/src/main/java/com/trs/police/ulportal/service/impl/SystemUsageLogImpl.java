package com.trs.police.ulportal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.grt.condify.parser.MybatisSearchParser;
import com.trs.police.ulportal.common.constants.ExceptionEnum;
import com.trs.police.ulportal.common.handler.PortalException;
import com.trs.police.ulportal.common.util.ResultHelper;
import com.trs.police.ulportal.common.util.UserUtil;
import com.trs.police.ulportal.converter.SystemUsageLogConverter;
import com.trs.police.ulportal.domain.dto.SystemUsageDto;
import com.trs.police.ulportal.domain.dto.SystemUsageLogDto;
import com.trs.police.ulportal.domain.entity.SystemUsageLog;
import com.trs.police.ulportal.domain.vo.*;
import com.trs.police.ulportal.mapper.SystemUsageLogMapper;
import com.trs.police.ulportal.service.SystemUsageLogService;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-12-05 21:21
 */
@Service
@RequiredArgsConstructor
public class SystemUsageLogImpl extends ServiceImpl<SystemUsageLogMapper, SystemUsageLog> implements SystemUsageLogService {

    private final SystemUsageLogMapper systemUsageLogMapper;

    private final SystemUsageLogConverter systemUsageLogConverter;

    @Override
    public RestfulResultsV2<SystemUsageLogVO> queryForPage(IPage<SystemUsageLog> page, QueryWrapper<SystemUsageLog> queryWrapper) {
        IPage<SystemUsageLog> result = systemUsageLogMapper.selectPage(page, queryWrapper);
        return ResultHelper.getIPageConverter().convert(systemUsageLogConverter.toPageVo(result));
    }

    @Override
    public RestfulResultsV2 saveSystemUsageLog(SystemUsageLogDto systemUsageLogDto) throws PortalException {
        SystemUsageLog systemUsageLog = systemUsageLogConverter.toEntity(systemUsageLogDto);
        //添加其他从token中解析出来的字段
        PortalUserVO loginUser = UserUtil.getLoginUser();
        if (loginUser == null) {
            throw new PortalException(ExceptionEnum.EX_LOGIN_USER_ERROR);
        }
        systemUsageLog.setIdEntityCard(loginUser.getIdEntityCard());
        systemUsageLog.setUserName(loginUser.getUserName());
        systemUsageLog.setOrgCode(loginUser.getOrgCode());
        systemUsageLog.setOrgName(loginUser.getOrgName());
        systemUsageLog.setCreateTime(LocalDateTime.now());

        saveOrUpdate(systemUsageLog);
        return RestfulResultsV2.ok("保存系统使用记录成功！");
    }

    @Override
    public RestfulResultsV2<TotalSystemUsageVO> getTotalSystemUsage(SystemUsageDto systemUsageDto) {

        TotalSystemUsageVO totalSystemUsageVO = systemUsageLogMapper.getTotalSystemUsage(systemUsageDto);
        return RestfulResultsV2.ok(totalSystemUsageVO);
    }

    @Override
    public RestfulResultsV2<SystemUsageVO> getSystemUsage(SystemUsageDto systemUsageDto) throws PortalException {
        IPage<SystemUsageVO> page = MybatisSearchParser.buildPage(systemUsageDto);

        IPage<SystemUsageVO> result = systemUsageLogMapper.getSystemUsageByOperLog(page, systemUsageDto);

        return ResultHelper.getIPageConverter().convert(result);
    }

    @Override
    public RestfulResultsV2<OrganSystemUsageVO> getOrganSystemUsage(SystemUsageDto systemUsageDto) {
        IPage<OrganSystemUsageVO> page = MybatisSearchParser.buildPage(systemUsageDto);

        IPage<OrganSystemUsageVO> result = systemUsageLogMapper.getOrganSystemUsage(page, systemUsageDto);

        return ResultHelper.getIPageConverter().convert(result);
    }


}
