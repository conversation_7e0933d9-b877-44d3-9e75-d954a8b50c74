package com.trs.police.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.common.core.request.ListParamsRequest;
import com.trs.police.search.domain.entity.SearchLogEntity;
import com.trs.police.search.domain.vo.LogDetailVO;
import com.trs.police.search.domain.vo.LogListVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 检索日志mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SearchLogMapper extends BaseMapper<SearchLogEntity> {

    /**
     * 查询日志列表
     *
     * @param request 参数
     * @param page    分页
     * @return 结果
     */
    Page<LogListVO> selectLogPage(@Param("request") ListParamsRequest request, Page<LogListVO> page);

    /**
     * 根据id查询
     *
     * @param id 日志id
     * @return 结果
     */
    LogDetailVO selectLogById(@Param("id") Long id);

    /**
     * 根据uuid查询
     *
     * @param uuid uuid
     * @return 结果
     */
    @Select("select * from t_search_log where uuid = #{uuid}")
    List<SearchLogEntity> selectByUuid(@Param("uuid") String uuid);
}
