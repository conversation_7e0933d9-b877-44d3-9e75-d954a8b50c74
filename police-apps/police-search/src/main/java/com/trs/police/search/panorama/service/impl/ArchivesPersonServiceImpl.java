package com.trs.police.search.panorama.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.exception.ServiceException;
import com.trs.common.pojo.OrderDTO;
import com.trs.common.utils.StringUtils;
import com.trs.police.search.domain.entity.PersonGroupRelation;
import com.trs.police.search.domain.entity.SearchFootholdEntity;
import com.trs.police.search.domain.request.BasicSourceDTO;
import com.trs.police.search.domain.request.GroupInfoDTO;
import com.trs.police.search.domain.vo.BasicSourceVO;
import com.trs.police.search.domain.vo.GroupInfoVO;
import com.trs.police.search.mapper.PersonGroupRelationMapper;
import com.trs.police.search.mapper.SearchFootholdMapper;
import com.trs.police.search.panorama.dto.PersonFootholdDTO;
import com.trs.police.search.panorama.service.IArchivesPersonService;
import com.trs.police.search.panorama.vo.PersonFootholdCountVO;
import com.trs.police.search.panorama.vo.PersonFootholdDateCountVO;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName ArchivesPersonServiceImpl
 * @Description 人员档案相关操作业务类
 * <AUTHOR>
 * @Date 2023/12/5 10:04
 **/
@Service
@AllArgsConstructor
public class ArchivesPersonServiceImpl implements IArchivesPersonService {

    private final SearchFootholdMapper footholdMapper;

    private final PersonGroupRelationMapper relationMapper;

    @Override
    public IPage<PersonFootholdCountVO> countList(PersonFootholdDTO dto) throws ServiceException {
        dto.isValid();
        Page<SearchFootholdEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        final List<OrderItem> orderItems = buildOrder(dto.getOrderList());
        if (CollectionUtils.isEmpty(orderItems)) {
            page.setOrders(Collections.singletonList(OrderItem.desc("zhcxsj")));
        } else {
            page.setOrders(orderItems);
        }
        return footholdMapper.countList(page, dto);
    }

    @Override
    public IPage<PersonFootholdDateCountVO> dateCountList(PersonFootholdDTO dto) throws ServiceException {
        PreConditionCheck.checkNotEmpty(dto.getFactorType(), "要素标识符不能为空！");
        PreConditionCheck.checkNotEmpty(dto.getFactorValue(), "要素值不能为空！");
        PreConditionCheck.checkNotEmpty(dto.getXxdz(), "落脚点地址信息不能为空！");
        Page<SearchFootholdEntity> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        final List<OrderItem> orderItems = buildOrder(dto.getOrderList());
        if (CollectionUtils.isEmpty(orderItems)) {
            page.setOrders(Collections.singletonList(OrderItem.desc("date")));
        } else {
            page.setOrders(orderItems);
        }
        return footholdMapper.dateCountList(page, dto);
    }

    private List<OrderItem> buildOrder(List<OrderDTO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>(0);
        }
        List<OrderItem> result = new ArrayList<>();
        for (OrderDTO orderDTO : orderList) {
            if (orderDTO.isDesc()) {
                result.add(OrderItem.desc(orderDTO.getFieldName()));
            } else {
                result.add(OrderItem.asc(orderDTO.getFieldName()));
            }
        }
        return result;
    }

    @Override
    public IPage<GroupInfoVO> groupInfoBySfzh(GroupInfoDTO dto) throws ServiceException {
        dto.isValid();
        //同一个身份证号在人员档案中应是唯一的
        Page<PersonGroupRelation> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return relationMapper.groupInfoBySfzh(page, dto);
    }

    @Override
    public IPage<BasicSourceVO> basicSourceInfo(BasicSourceDTO dto) throws ServiceException {
        dto.isValid();
        List<String> typeList = StringUtils.isEmpty(dto.getSourceType()) ? new ArrayList<>()
                : Arrays.asList(dto.getSourceType().split(StringUtils.SEPARATOR_COMMA));
        Page<BasicSourceVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        //经纬度为空
        if (StringUtils.isEmpty(dto.getLongitude())) {
            return relationMapper.querySource(page, dto, typeList);
        } else {
            return relationMapper.basicSourceInfo(page, dto, typeList);
        }
    }
}
