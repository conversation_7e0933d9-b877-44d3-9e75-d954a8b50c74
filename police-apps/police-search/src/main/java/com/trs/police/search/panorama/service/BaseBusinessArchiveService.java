package com.trs.police.search.panorama.service;

import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.db.sdk.pojo.BaseRecordDO;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.dto.AiLabelDTO;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.vo.AiLabelVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2024</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * @param <Target> 参数
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/5/16 22:31
 * @since 1.0
 */
public abstract class BaseBusinessArchiveService<Target extends BaseRecordDO> extends BaseArchiveService<Target> {

    private List<String> entityRelationObjTypes = List.of(
            ArchivesEnum.PERSON.getArchiveRelationObjType(),
            ArchivesEnum.CAR.getArchiveRelationObjType(),
            ArchivesEnum.PHONE.getArchiveRelationObjType()
    );

    public BaseBusinessArchiveService(ISearchFoundationService searchService) {
        super(searchService);
    }

    /**
     * convertObjType<BR>
     *
     * @param objType 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/1/21 19:21
     */
    private String convertObjType(String objType) {
        switch (StringUtils.showEmpty(objType)) {
            case "案件":
            case "案事件编号":
                return "案件";
            case "警情":
            case "警情编号":
                return "警情";
            case "线索":
            case "协同":
            case "风险":
            default:
                return objType;
        }
    }

    /**
     * convertAiLabel<BR>
     *
     * @param dto    参数
     * @param labels 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/5/16 23:04
     */
    @Override
    public Tuple2<Map<String, List<AiLabelVo>>, String> convertAiLabel(AiLabelDTO dto, List<AiLabelVo> labels) throws ServiceException {
        if (CollectionUtils.isEmpty(labels)) {
            return Tuple.of(Collections.emptyMap(), null);
        }
        var relationObjTypeMap = labels.stream()
                .filter(it -> entityRelationObjTypes.contains(it.getRelationObjType()))
                .collect(Collectors.groupingBy(AiLabelVo::getRelationObjType));
        if (relationObjTypeMap.isEmpty()) {
            return Tuple.of(Collections.emptyMap(), null);
        }
        final var search = makeExtPageList(dto,
                dto.getRelationTableName(),
                And(
                        Not(
                                And(
                                        ArchivesUtils.makeTargetExpression("obj_type", archivesEnum().getArchiveRelationObjType()),
                                        ArchivesUtils.makeTargetExpression("obj", dto.getRecordId())
                                )
                        ),
                        Or(
                                relationObjTypeMap.keySet().stream().map(relationObjType -> And(
                                        ArchivesUtils.makeTargetExpression("relation_obj_type", relationObjType),
                                        Condition(
                                                "relation_obj",
                                                EsOperator.SimpleQueryString,
                                                relationObjTypeMap.get(relationObjType)
                                                        .stream()
                                                        .map(AiLabelVo::getRelationObj)
                                                        .map(it -> String.format("\"%s\"", it))
                                                        .distinct()
                                                        .collect(Collectors.joining("|"))
                                        )
                                )).toArray(Expression[]::new)
                        )
                ));
        var objs = search.getContents()
                .stream()
                .peek(it -> {
                    List<String> center = new ArrayList<>(2);
                    // 重置名称
                    it.setObjType(convertObjType(it.getObjType()));
                    it.setRelationObjType(convertObjType(it.getRelationObjType()));
                    switch (it.getObjType()) {
                        case "案件":
                        case "案事件编号":
                            Optional.ofNullable(it.getExt()).map(r -> r.getString("ajmc"))
                                    .filter(StringUtils::isNotEmpty)
                                    .ifPresent(center::add);
                            center.add(it.getObj());
                            break;
                        case "警情":
                        case "警情编号":
                            Optional.ofNullable(it.getExt()).map(r -> r.getString("jyjq"))
                                    .filter(StringUtils::isNotEmpty)
                                    .ifPresent(center::add);
                            center.add(it.getObj());
                            break;
                        case "线索":
                            Optional.ofNullable(it.getExt()).map(r -> r.getString("xsmc"))
                                    .filter(StringUtils::isNotEmpty)
                                    .ifPresent(center::add);
                            center.add(it.getObj());
                            break;
                        case "协同":
                            Optional.ofNullable(it.getExt()).map(r -> r.getString("xtzt"))
                                    .filter(StringUtils::isNotEmpty)
                                    .ifPresent(center::add);
                            center.add(it.getObj());
                            break;
                        default:
                    }
                    it.setCenter(center);
                }).filter(it -> CollectionUtils.isNotEmpty(it.getCenter())).collect(Collectors.groupingBy(AiLabelVo::getObjType));
        Map<String, List<AiLabelVo>> target = new HashMap<>(objs);
        if (target.isEmpty()) {
            return Tuple.of(Collections.emptyMap(), null);
        }
        var relationObjAndTypeMap = labels.stream()
                .collect(Collectors.groupingBy(it -> it.getRelationObjType() + "&&&" + it.getRelationObj()));
        Map<String, List<AiLabelVo>> map = new HashMap<>(labels.size());
        final boolean needMergeDuplicate = BeanFactoryHolder.getEnv().getProperty("search.businessArchives.aiLabelVo.needMergeDuplicate", Boolean.class, true);
        target.forEach((relationCatalogClass1, v1) -> v1.stream()
                .collect(Collectors.groupingBy(it -> it.getRelationObjType() + "&&&" + it.getRelationObj()))
                .forEach((k, v) -> {
                    var relationObjs = relationObjAndTypeMap.get(k);
                    if (CollectionUtils.isEmpty(relationObjs)) {
                        return;
                    }
                    if (needMergeDuplicate) {
                        relationObjs.stream()
                                .filter(Objects::nonNull)
                                .findFirst()
                                .ifPresent(relationObj -> {
                                    AiLabelVo vo = relationObj.copy();
                                    List<String> center = new ArrayList<>(2);
                                    vo.setRelationCatalog(relationCatalogClass1);
                                    vo.setTopRight(
                                            Optional.of(relationObjs.stream()
                                                            .map(AiLabelVo::getRelation)
                                                            .filter(StringUtils::isNotEmpty)
                                                            .distinct()
                                                            .collect(Collectors.toList()))
                                                    .filter(CollectionUtils::isNotEmpty)
                                                    .orElseGet(() -> List.of(relationObj.getRelationCatalog()))
                                    );
                                    switch (StringUtils.showEmpty(relationObj.getRelationObjType())) {
                                        case "车":
                                        case "车牌号":
                                        case "手机号":
                                            center.add(relationObj.getRelationObj());
                                            break;
                                        case "人":
                                        case "身份证":
                                            Optional.ofNullable(relationObj.getExt()).map(r -> r.getString("xm"))
                                                    .filter(StringUtils::isNotEmpty)
                                                    .ifPresent(center::add);
                                            center.add(relationObj.getRelationObj());
                                            break;
                                        default:
                                    }
                                    vo.setCenter(center);
                                    final List<AiLabelVo> tmp = map.getOrDefault(relationCatalogClass1, new ArrayList<>(1));
                                    vo.setChildren(v);
                                    tmp.add(vo);
                                    map.put(relationCatalogClass1, tmp);
                                });
                    } else {
                        for (AiLabelVo relationObj : relationObjs) {
                            if (relationObj != null) {
                                AiLabelVo vo = relationObj.copy();
                                List<String> center = new ArrayList<>(2);
                                vo.setRelationCatalog(relationCatalogClass1);
                                vo.setTopRight(List.of(StringUtils.showEmpty(relationObj.getRelation(), relationObj.getRelationCatalog())));
                                switch (StringUtils.showEmpty(relationObj.getRelationObjType())) {
                                    case "车":
                                    case "车牌号":
                                    case "手机号":
                                        center.add(relationObj.getRelationObj());
                                        break;
                                    case "人":
                                    case "身份证":
                                        Optional.ofNullable(relationObj.getExt()).map(r -> r.getString("xm"))
                                                .filter(StringUtils::isNotEmpty)
                                                .ifPresent(center::add);
                                        center.add(relationObj.getRelationObj());
                                        break;
                                    default:
                                }
                                vo.setCenter(center);
                                final List<AiLabelVo> tmp = map.getOrDefault(relationCatalogClass1, new ArrayList<>(1));
                                vo.setChildren(v);
                                tmp.add(vo);
                                map.put(relationCatalogClass1, tmp);
                            }
                        }
                    }
                }));
        return Tuple.of(map, search.getCondition());
    }
}
