package com.trs.police.search.panorama.service.impl.search;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ServiceException;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.police.common.core.constant.enums.VirtualIdentityTypeEnum;
import com.trs.police.common.core.constant.search.ArchivesConstants;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import com.trs.police.common.core.vo.search.ArchivesVO;
import com.trs.police.common.core.vo.search.KeyValueTypeVoForSearch;
import com.trs.police.common.openfeign.starter.DTO.BaseArchivesSearchDTO;
import com.trs.police.search.panorama.constant.enums.ArchivesEnum;
import com.trs.police.search.panorama.context.SceneContext;
import com.trs.police.search.panorama.manager.impl.ArchivesDetailSceneManager;
import com.trs.police.search.panorama.manager.impl.ArchivesListSceneManager;
import com.trs.police.search.panorama.service.BaseMultiPlatformSearchService;
import com.trs.police.search.panorama.util.ArchivesUtils;
import com.trs.police.search.panorama.util.PhoneVirtualInfoHelper;
import com.trs.police.search.panorama.vo.ExtPageList;
import com.trs.police.search.repository.ThemeGjxxbRepository;
import com.trs.web.entity.Order;
import com.trs.web.entity.PageList;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.expression.ExpressionBuilder.And;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：手机档案刷新虚拟身份信息（IMSI，IMEI）
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/2/7 11:42
 * @since 1.0
 */
@Service
@Slf4j
public class PhoneFlushVirtualServiceImpl extends BaseMultiPlatformSearchService {

    private final ArchivesDetailSceneManager detailSceneManager;
    private final ArchivesListSceneManager listSceneManager;
    private final PhoneVirtualInfoHelper phoneVirtualInfoHelper;
    private final ThemeGjxxbRepository themeGjxxbRepository;

    public PhoneFlushVirtualServiceImpl(
            ArchivesListSceneManager listSceneManager,
            ArchivesDetailSceneManager detailSceneManager,
            ThemeGjxxbRepository themeGjxxbRepository,
            @Nullable PhoneVirtualInfoHelper phoneVirtualInfoHelper
    ) {
        this.listSceneManager = listSceneManager;
        this.detailSceneManager = detailSceneManager;
        this.themeGjxxbRepository = themeGjxxbRepository;
        this.phoneVirtualInfoHelper = phoneVirtualInfoHelper;
        log.info("初始化[{}],Flag=[{}]", desc(), supportArchiveType(ArchivesConstants.ARCHIVES_TYPE_ALL));
    }

    /**
     * getFlushTime<BR>
     *
     * @param dto 参数
     * @return 结果
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/2/11 16:17
     */
    public String getFlushTime(BaseArchivesSearchDTO dto) throws ServiceException {
        var recordId = dto.getRecordId();
        if (!ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PHONE)
                .map(it -> it.check(recordId)).orElse(false)) {
            throw new ServiceException("[" + recordId + "]不是手机号");
        }
        String redisKey = "PhoneFlushVirtualServiceImpl:Phone:" + recordId;
        return StringUtils.showEmpty(
                (String) redisService.get(redisKey),
                TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS)
        );
    }

    @Override
    public void flushDataOnDetail(String recordId) {
        log.info("开始刷新手机号[{}]", recordId);
        if (!ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PHONE)
                .map(it -> it.check(recordId)).orElse(false)) {
            log.warn("[{}]不是手机号，跳过刷新", recordId);
            return;
        }
        String redisKey = "PhoneFlushVirtualServiceImpl:Phone:" + recordId;
        Object time = redisService.get(redisKey);
        if (Objects.nonNull(time)) {
            log.warn("[{}]在[{}]已经刷新过了，跳过此次刷新", recordId, time);
            return;
        }
        var t = Try.of(() -> {
            BaseArchivesSearchDTO dto = new BaseArchivesSearchDTO();
            dto.setArchivesType(ArchivesEnum.PHONE.getType());
            dto.setRecordId(recordId);
            dto.setNeedLog(false);
            dto.setNeedAuth(false);
            return detailSceneManager.findByDTO(dto)
                    .orElseThrow(() -> new ServiceException("数据[" + recordId + "]未入库ES"));
        }).onFailure(e -> log.warn("数据[{}]查询异常", recordId, e));
        if (t.isSuccess()) {
            final Map<String, Object> phoneVirtualInfoMap = phoneVirtualInfoHelper.getPhoneVirtualInfoMap(recordId);
            final List<String> imsis = Optional.ofNullable(phoneVirtualInfoMap)
                    .map(it -> it.get("imsis"))
                    .map(it -> (List<String>) it)
                    .orElse(List.of());
            final List<String> imeis = Optional.ofNullable(phoneVirtualInfoMap)
                    .map(it -> it.get("imeis"))
                    .map(it -> (List<String>) it)
                    .orElse(List.of());
            final List<String> macs = Optional.ofNullable(phoneVirtualInfoMap)
                    .map(it -> it.get("macs"))
                    .map(it -> (List<String>) it)
                    .orElse(List.of());
            if (CollectionUtils.isEmpty(imeis) && CollectionUtils.isEmpty(imsis) && CollectionUtils.isEmpty(macs)) {
                log.warn("手机号[{}]没有获取到对应数据，跳过刷新", recordId);
                return;
            }
            var vo = t.get();
            final ArchivesVO target = new ArchivesVO();
            target.setType(vo.getType());
            target.setRecordId(vo.getRecordId());
            target.setOrder(vo.getOrder());
            target.setSecondRecordId(vo.getSecondRecordId());
            target.setFields(makeUpdateFields(imeis, imsis, macs));
            log.info("手机号[{}]更新的数据为[{}]", recordId, target);
            resultSendMessage(List.of(target));
            if (CollectionUtils.isNotEmpty(imsis)) {
                // 增加锁，30天内只刷新一次
                redisService.set(redisKey, TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS), 60 * 60 * 24 * 30L);
            } else {
                // 增加锁，24小时内只调度一次
                redisService.set(redisKey, TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS), 60 * 60 * 24L);
            }
        } else {
            log.warn("手机号[{}]查询异常", recordId);
            // 增加锁，失败时24小时内只调度一次
            redisService.set(redisKey, TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS), 60 * 60 * 24L);
        }
        log.info("结束刷新手机号[{}]", recordId);
    }

    /**
     * makeUpdateFields<BR>
     *
     * @param imeis 参数
     * @param imsis 参数
     * @param macs  参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/2/11 16:23
     */
    private List<KeyValueTypeVoForSearch> makeUpdateFields(List<String> imeis, List<String> imsis, List<String> macs) {
        List<KeyValueTypeVoForSearch> fields = new ArrayList<>(6);
        if (CollectionUtils.isNotEmpty(imeis)) {
            JSONArray data = new JSONArray();
            for (String s : imeis) {
                JSONObject item = new JSONObject();
                item.put("imei", s);
                item.put("reason", "530接口获取");
                data.add(item);
            }
            fields.add(KeyValueTypeVoForSearch.of("imeis", data));
            fields.add(KeyValueTypeVoForSearch.of("imei", findLastValue(VirtualIdentityTypeEnum.IMEI, imeis)));
        }
        if (CollectionUtils.isNotEmpty(imsis)) {
            JSONArray data = new JSONArray();
            for (String s : imsis) {
                JSONObject item = new JSONObject();
                item.put("imsi", s);
                item.put("reason", "530接口获取");
                data.add(item);
            }
            fields.add(KeyValueTypeVoForSearch.of("misis", data));
            fields.add(KeyValueTypeVoForSearch.of("imsis", data));
            fields.add(KeyValueTypeVoForSearch.of("imsi", findLastValue(VirtualIdentityTypeEnum.IMSI, imsis)));
        }
        if (CollectionUtils.isNotEmpty(macs)) {
            JSONArray data = new JSONArray();
            for (String s : macs) {
                JSONObject item = new JSONObject();
                item.put("mac", s);
                item.put("reason", "530接口获取");
                data.add(item);
            }
            fields.add(KeyValueTypeVoForSearch.of("macs", data));
            fields.add(KeyValueTypeVoForSearch.of("mac", findLastValue(VirtualIdentityTypeEnum.MAC, macs)));
        }
        return fields;
    }

    /**
     * 获取最后的数据<BR>
     *
     * @param type 参数
     * @param list 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2025/2/7 14:16
     */
    private String findLastValue(VirtualIdentityTypeEnum type, List<String> list) {
        if (list.size() > 1) {
            return Optional.ofNullable(
                    themeGjxxbRepository.findFirst(
                            And(
                                    Condition(
                                            "tzlx",
                                            EsOperator.SimpleQueryString,
                                            Stream.of(type.getName().toUpperCase(), type.getName())
                                                    .map(it -> String.format("\"%s\"", it))
                                                    .collect(Collectors.joining("|"))
                                    ),
                                    Condition(
                                            "tzzhm",
                                            EsOperator.SimpleQueryString,
                                            list.stream()
                                                    .map(it -> String.format("\"%s\"", it))
                                                    .collect(Collectors.joining("|"))
                                    )
                            ),
                            Order.desc("hdsj")
                    )
            ).map(ThemeGjxxbEntity::getTzzhm).orElse(list.get(0));
        }
        // 查询不到直接用第一条数据
        return list.get(0);
    }

    @Override
    public Boolean supportFlushOnDetail() {
        return true;
    }

    @Override
    public Boolean resultNeedSendMessage() {
        return true;
    }

    @Override
    public boolean checkNeedSendMessage(List<ArchivesVO> result) {
        // 标记的行为不进行判断
        return CollectionUtils.isNotEmpty(result);
    }

    @Override
    public ExtPageList<ArchivesVO> doSearchData(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("检索器[{}]开始搜索[{}]", desc(), dto.getKeyword());
                BaseArchivesSearchDTO searchDTO = new BaseArchivesSearchDTO();
                searchDTO.setNeedLog(false);
                searchDTO.setNeedAuth(false);
                searchDTO.setArchivesType(ArchivesEnum.PHONE.getType());
                searchDTO.setRecordId(dto.getKeyword());
                // 这个数据不需要搜索，只需要刷新数据即可
                if (ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PERSON)
                        .map(it -> it.check(dto.getKeyword())).orElse(false)) {
                    searchDTO.setFieldName(ArchivesEnum.PHONE.getPersonFieldName());
                } else if (ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PHONE)
                        .map(it -> it.check(dto.getKeyword())).orElse(false)) {
                    searchDTO.setFieldName(ArchivesEnum.PHONE.getDetailField());
                }
                Optional<ExtPageList<ArchivesVO>> opt = listSceneManager.fetchData(SceneContext.of(searchDTO));
                opt.map(PageList::getContents).filter(CollectionUtils::isNotEmpty)
                        .ifPresent(it -> it.forEach(item -> {
                            String recordId = item.getRecordId();
                            Try.run(() -> flushDataOnDetail(recordId))
                                    .onFailure(e -> log.error("手机号[{}]刷新错误", recordId, e));
                        }));
            } catch (Exception e) {
                log.error("手机档中根据[{}]刷新虚拟身份信息出错", dto.getKeyword(), e);
            }
        }, EXECUTOR_SERVICE);
        return getNoDataPageList(pageNum, pageSize, "", 0L);
    }

    @Override
    public String archiveType() {
        return ArchivesConstants.ARCHIVES_TYPE_ALL;
    }

    /**
     * 是否支持对应平台<BR>
     *
     * @param archiveType 检索类型
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/1/31 10:11
     */
    @Override
    public Boolean supportArchiveType(String archiveType) {
        if (StringUtils.isEmpty(archiveType)) {
            return false;
        }
        return Objects.nonNull(this.phoneVirtualInfoHelper);
    }

    @Override
    public Boolean checkNeedSearch(Integer pageNum, Integer pageSize, BaseArchivesSearchDTO dto) {
        // 是否支持检索
        if (!supportArchiveType(dto.getArchivesType())) {
            return false;
        }
        // 检索词是否为空
        if (StringUtils.isEmpty(dto.getKeyword())) {
            return false;
        }
        // 是否满足父类检索要求
        if (!super.checkNeedSearch(pageNum, pageSize, dto)) {
            return false;
        }
        var archivesEnum = ArchivesEnum.getInstance(dto.getArchivesType());
        if (archivesEnum == null) {
            return false;
        }
        switch (archivesEnum) {
            case ALL:
            case PHONE:
                // 机主身份证跟手机号两个搜索
                return ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PERSON)
                        .map(it -> it.check(dto.getKeyword())).orElse(false)
                        || ArchivesUtils.findCheckAndParse(ArchivesConstants.ARCHIVES_TYPE_PHONE)
                        .map(it -> it.check(dto.getKeyword())).orElse(false);
            default:
                return false;
        }
    }

    @Override
    public Integer order() {
        return 31;
    }

    @Override
    public String desc() {
        return "手机档案刷新虚拟身份信息（IMSI，IMEI）";
    }
}
