package com.trs.police.search.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2023</p>
 * <p>Company:      www.trs.com.cn</p>
 * SearchLabelEntity
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2023/9/27 17:28
 * @since 1.0
 */
@Data
@TableName("tb_search_label")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchLabelEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标签Key方便后续对其进行单独处理
     */
    @TableField
    private String searchKey;

    /**
     * 标签是否支持二次检索
     */
    @TableField
    private Integer supportCondition;

    /**
     * 标签名称
     */
    @TableField
    private String name;

    /**
     * 标签属性（数据量偏多等）
     */
    @TableField
    private String nature;

    /**
     * 标签类型
     */
    @TableField
    private String labelType;

    /**
     * 标签类型
     */
    @TableField
    private String labelTypeName;

    /**
     * 标签所属档案类型
     */
    @TableField
    private String archivesType;

    /**
     * 标签所属档案类型
     */
    @TableField
    private String archivesTypeName;

    @TableField
    private String color;

}
