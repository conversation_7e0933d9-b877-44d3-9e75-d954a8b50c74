package com.trs.police.spacetime.collision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.spacetime.collision.domain.entity.CollisionJobEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * 碰撞任务mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CollisionJobMapper extends BaseMapper<CollisionJobEntity> {

    /**
     * 根据碰撞id查询最近一个碰撞任务
     *
     * @param collisionId 碰撞id
     * @return 任务
     */
    @ResultMap("mybatis-plus_CollisionJobEntity")
    @Select(value = "select * from t_spacetime_collision_job where collision_id = #{collisionId} order by id desc limit 1")
    CollisionJobEntity getRecentJobByCollisionId(@Param("collisionId") Long collisionId);
}
