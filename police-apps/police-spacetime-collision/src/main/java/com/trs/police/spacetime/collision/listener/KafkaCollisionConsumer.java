package com.trs.police.spacetime.collision.listener;

import com.trs.common.utils.StringUtils;
import com.trs.police.spacetime.collision.properties.KafkaCollisionConsumerProperties;
import com.trs.police.spacetime.collision.service.CollisionResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：时空碰撞消息消费者
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/3 09:38
 * @since 1.0
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaCollisionConsumerProperties.class)
public class KafkaCollisionConsumer {

    private final KafkaConsumer<String, String> consumer;

    private final KafkaCollisionConsumerProperties properties;

    private final CollisionResultService collisionResultService;

    public KafkaCollisionConsumer(KafkaCollisionConsumerProperties properties, CollisionResultService collisionResultService) {
        this.properties = properties;
        this.collisionResultService = collisionResultService;
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getBootStrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, properties.getGroupId());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, properties.getMaxPollRecords());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, properties.getAutoOffsetReset());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        //关闭自动提交kafka事务，批量处理时控制回滚策略
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        if (!CollectionUtils.isEmpty(properties.getProperties())) {
            props.putAll(properties.getProperties());
        }
        if (Objects.equals(Boolean.TRUE, properties.getEnableKerberos())) {
            props.put("security.protocol", "SASL_PLAINTEXT");
            props.put("sasl.kerberos.service.name", "kafka");
            props.put("kerberos.domain.name", StringUtils.showEmpty(properties.getKerberosDomain(), "hadoop.hadoop.com"));
        }
        consumer = new KafkaConsumer<>(props);
        consumer.subscribe(List.of(properties.getTopic()));
    }

    /**
     * 消费时空碰撞消息
     */
    @Scheduled(fixedDelay = 1L)
    public void consumer() {
        final ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(properties.getPollDuration()));
        log.info("本批次数据共消费：{} 条", records.count());
        List<ConsumerRecord<String, String>> recordList = new ArrayList<>(records.count());
        records.forEach(recordList::add);
        //处理该批次消费到的数据
        recordList.forEach(result -> {
            String message = result.value();
            log.info("接收到时空碰撞消息：{}", message);
            if (StringUtils.isNotEmpty(message)) {
                log.error("receive kafka message blank!");
                return;
            }
            try {
                collisionResultService.processCollision(message);
            } catch (Exception e) {
                log.info("处理时空碰撞消息失败！", e);
            }
        });
        log.info("本批次消费数据入库完毕！");
        //提交kafka事务
        consumer.commitSync();
    }
}
