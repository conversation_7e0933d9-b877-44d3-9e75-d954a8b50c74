package com.trs.police.spacetime.collision.domain.vo;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/3 09:26
 * @since 1.0
 */
@Data
public class CollisionKafkaMessageVo extends BaseVO {

    private Integer code;

    private String message;

    private Boolean success;

    private MessageData data;

    /**
     * <p>Title:        TRS</p>
     * <p>Copyright:    Copyright (c) 2004-2025</p>
     * <p>Company:      www.trs.com.cn</p>
     * 类描述：
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @version 1.0
     * @date 创建时间：2025/6/3 09:29
     * @since 1.0
     */
    @Data
    public static final class MessageData extends BaseVO {

        /**
         * 作业id，作为查询结果时使用
         */
        private String jobId;

        /**
         * 作业name，作为查询结果时使用
         */
        private String jobName;
    }
}
