package com.trs.police.profile.domain.entity.person;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人员-治安-党政管控信息
 *
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_profile_person_dz_control")
public class PersonDzControl extends PersonRelatedBase {
    /**
     * 化解责任单位
     */
    private String defuseGovernment;

    /**
     * 化解责任人
     */
    private String defuseGovernmentPerson;

    /**
     * 化解责任人联系方式
     */
    private String defuseGovernmentContact;

    /**
     * 稳控责任部门
     */
    private String controlGovernment;

    /**
     * 稳控责任人
     */
    private String controlGovernmentPerson;

    /**
     * 稳控责任人联系方式
     */
    private String controlGovernmentContact;

    /**
     * 包案县级领导
     */
    private String controlLeader;

    /**
     * 包案县级领导
     */
    private String controlLeaderDuty;

    /**
     * 社区责任人联系方式
     */
    private String controlLeaderContact;
}
