package com.trs.police.profile.domain.vo.graph;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 关系图谱VO
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GraphVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 节点列表
     */
    private List<GraphNodeVO<?>> nodes = new ArrayList<>();

    /**
     * 关系列表
     */
    private List<GraphLinkVO<?>> links = new ArrayList<>();
}
