package com.trs.police.profile.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.constant.enums.PoliceKindEnum;
import com.trs.police.common.core.dto.DistrictDto;
import com.trs.police.common.core.entity.Label;
import com.trs.police.common.core.excpetion.SystemException;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.vo.ImportFailVO;
import com.trs.police.common.core.vo.ImportResultVO;
import com.trs.police.common.core.vo.ZgEventBzPersonVO;
import com.trs.police.common.core.vo.oss.FileInfoVO;
import com.trs.police.common.core.vo.profile.LabelVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.common.openfeign.starter.service.OperationLogService;
import com.trs.police.common.openfeign.starter.service.OssService;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.profile.constant.ZgEventConstant;
import com.trs.police.profile.constant.enums.ZgPersonMatchEnum;
import com.trs.police.profile.converter.*;
import com.trs.police.profile.domain.entity.Person;
import com.trs.police.profile.excel.listener.*;
import com.trs.police.profile.excel.model.*;
import com.trs.police.profile.mapper.*;
import com.trs.police.profile.schema.service.approval.common.EventApprovalService;
import com.trs.police.profile.schema.service.approval.common.GoodsEditApprovalService;
import com.trs.police.profile.service.LabelService;
import com.trs.police.profile.service.mp.EventClueRelationService;
import com.trs.police.profile.service.mp.EventGroupRelationService;
import com.trs.police.profile.service.mp.EventPersonRelationService;
import com.trs.police.profile.service.mp.PersonMpService;
import com.trs.police.profile.util.EnvUtil;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/31 16:32
 */
@Service
@Slf4j
public class ImportService {

    @Resource
    LabelService labelService;
    @Resource
    OssService ossService;

    @Resource
    PersonMapper personMapper;

    @Resource
    GoodsMapper goodsMapper;

    @Resource
    GoodsRiskControlMapper goodsRiskControlMapper;
    @Autowired
    private PersonMpService personMpService;
    @Resource
    EventClueRelationMapper eventClueRelationMapper;

    @Resource
    EventGroupRelationMapper eventGroupRelationMapper;

    @Resource
    EventPersonRelationMapper eventPersonRelationMapper;

    @Resource
    ProfileVirtualIdentityMapper profileVirtualIdentityMapper;
    @Resource
    ProfilePersonPoliceControlMapper profilePersonPoliceControlMapper;
    @Resource
    ProfileVehicleMapper profileVehicleMapper;
    @Resource
    OperationLogService operationLogService;
    @Resource
    DictService dictService;
    @Resource
    PermissionService permissionService;
    @Autowired
    private PersonEventRelationMapper personEventRelationMapper;

    @Resource
    GroupMapper groupMapper;

    @Resource
    EventMapper eventMapper;

    @Resource
    ProfileGroupPoliceControlMapper profileGroupPoliceControlMapper;

    @Resource
    ProfileGroupGovControlMapper profileGroupGovControlMapper;

    @Autowired
    private GroupConverter groupConverter;

    @Autowired
    private JzGroupConverter jzGroupConverter;
    @Autowired
    private LabelMapper labelMapper;

    @Autowired
    private ZaGroupConverter zaGroupConverter;

    @Autowired
    private QtGroupConverter qtGroupConverter;

    @Resource
    private EventConverter eventConverter;

    @Resource
    private ClueMapper clueMapper;

    @Resource
    private ProfileSensitiveTimeMapper profileSensitiveTimeMapper;

    @Resource
    private EventPersonRelationService personRelationService;

    @Resource
    private EventClueRelationService clueRelationService;

    @Resource
    private EventGroupRelationService groupRelationService;

    @Resource
    private PersonGroupRelationMapper personGroupRelationMapper;

    @Value("${profile.group.exportRootDistrictCode:510000}")
    private String exportRootDistrictCode;

    @Resource
    private PersonClueRelationMapper personClueRelationMapper;

    @Resource
    private GroupClueRelationMapper groupClueRelationMapper;

    @Resource
    private GroupRiskOtherMapper groupRiskOtherMapper;

    @Resource
    private GroupDzControlEntityMapper groupDzControlEntityMapper;

    @Resource
    private EventApprovalService goodsApprovalService;

    @Resource
    private GoodsEditApprovalService goodsEditApprovalService;

    @Resource
    private GoodsOperationLogMapper goodsOperationLogMapper;

    private static final Path TEMP_DIR_PATH = Paths.get(System.getProperty("user.dir"), "temp");

    /**
     * 下载导入模板
     *
     * @param response 响应
     * @throws IOException io异常
     */
    public void downloadTemplate(HttpServletResponse response) throws IOException {
//        ResponseEntity<byte[]> entity = ossService.getByModule("profilePersonImportTemplate");
//        // 从ResponseEntity获取字节数组，然后创建InputStream
//        byte[] bytes = entity.getBody();

        String personTemplateName = BeanFactoryHolder.getEnv().getProperty("profile.import.template.person", "importPerson.xlsx");
        final InputStream in = this.getClass().getResourceAsStream("/template/" + personTemplateName);

        // 创建ByteArrayOutputStream以存储修改后的Excel文件
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        buildCommonLabel(in, out, null);

        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("人员档案导入模板.xlsx", StandardCharsets.UTF_8));

        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }

    /**
     * 下载群体导入模板
     *
     * @param response 响应
     * @throws IOException io异常
     */
    public void downloadGroupTemplate(HttpServletResponse response) throws IOException {
        String groupTemplateName = "importGroup.xlsx";
        if(EnvUtil.isDy()){
            groupTemplateName = "importGroupDy.xlsx";
        }
        final InputStream in = this.getClass().getResourceAsStream("/template/" + groupTemplateName);

        // 创建ByteArrayOutputStream以存储修改后的Excel文件
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        List<List<String>> groupLabelDataList = new ArrayList<>();
        List<LabelVO> group = labelService.getLabelListPermission("group");
        for (LabelVO labelVO : group) {
            traverseLabel(labelVO, groupLabelDataList, new ArrayList<>());
        }
        // 创建标签表头
        List<List<String>> tableHeadList = new ArrayList<>();
        List<String> id = new ArrayList<>();
        id.add("编号");
        tableHeadList.add(id);
        List<String> maxList = Collections.max(groupLabelDataList, Comparator.comparing(List::size));
        for (int i = 0; i < maxList.size() - 1; i++) {
            List<String> head = new ArrayList<>();
            head.add((i + 1) + "级标签");
            tableHeadList.add(head);
        }
        WriteSheet tableSheet = EasyExcel.writerSheet("群体类别")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(tableHeadList).build();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.write(groupLabelDataList, tableSheet);
        excelWriter.finish();

        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("群体档案导入模板.xlsx", StandardCharsets.UTF_8));

        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }

    /**
     * 下载群体事件模板
     *
     * @param response 响应
     * @throws IOException io异常
     */
    public void downloadEventTemplate(HttpServletResponse response) throws IOException {
        String templateFile = EnvUtil.isDy() ? "importEventDy.xlsx" : "importEvent.xlsx";
        final InputStream in = this.getClass().getResourceAsStream("/template/"+templateFile);

        // 创建ByteArrayOutputStream以存储修改后的Excel文件
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        //处理事件标签
        List<List<String>> eventLabelDataList = new ArrayList<>();
        List<LabelVO> event = labelService.getLabelListPermission("event");
        for (LabelVO labelVO : event) {
            traverseLabel(labelVO, eventLabelDataList, new ArrayList<>());
        }
        // 创建标签表头
        List<List<String>> tableHeadList = new ArrayList<>();
        List<String> id = new ArrayList<>();
        id.add("编号");
        tableHeadList.add(id);
        List<String> maxList = Collections.max(eventLabelDataList, Comparator.comparing(List::size));
        for (int i = 0; i < maxList.size() - 1; i++) {
            List<String> head = new ArrayList<>();
            head.add((i + 1) + "级标签");
            tableHeadList.add(head);
        }
        WriteSheet tableSheet = EasyExcel.writerSheet("事件类别")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(tableHeadList).build();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.write(eventLabelDataList, tableSheet);
        excelWriter.finish();

        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("事件档案导入模板.xlsx", StandardCharsets.UTF_8));

        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }

    /**
     * 构建公共标签
     *
     * @param in  in
     * @param out out
     * @param policeKind 警种
     */
    public void buildCommonLabel(InputStream in, ByteArrayOutputStream out, Integer policeKind) {
        List<List<String>> personLabelDataList = new ArrayList<>();
        String labelType =  Objects.equals(6, policeKind) ? "entry_exit_person": "person";
        List<LabelVO> person = labelService.getLabelListPermission(labelType);
        for (LabelVO labelVO : person) {
            traverseLabel(labelVO, personLabelDataList, new ArrayList<>());
        }
        // 创建标签表头
        List<List<String>> tableHeadList = new ArrayList<>();
        List<String> id = new ArrayList<>();
        id.add("编号");
        tableHeadList.add(id);
        List<String> maxList = Collections.max(personLabelDataList, Comparator.comparing(List::size));
        for (int i = 0; i < maxList.size() - 1; i++) {
            List<String> head = new ArrayList<>();
            head.add((i + 1) + "级标签");
            tableHeadList.add(head);
        }
        WriteSheet tableSheet = EasyExcel.writerSheet("人员标签")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(tableHeadList).build();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.write(personLabelDataList, tableSheet);

        // 处理地域标签
        List<DistrictDto> districtTree = dictService.getDistrictTree(Objects.equals(6, policeKind) ? "510300":exportRootDistrictCode);
        List<List<String>> districtDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(districtTree) && districtTree.size() == 1 && "100000".equals(districtTree.get(0).getCode())) {
            districtTree = districtTree.get(0).getChildren();
        }
        for (DistrictDto districtDto : districtTree) {
            traverseDistrict(districtDto, districtDataList, new ArrayList<>());
        }

        final List<List<String>> districtTableHeadList = new ArrayList<>();
        List<String> list1 = new ArrayList<>();
        list1.add("编码");
        List<String> list2 = new ArrayList<>();
        list2.add(Objects.equals(6, policeKind) ? "市" : "省");
        List<String> list3 = new ArrayList<>();
        list3.add(Objects.equals(6, policeKind) ? "区县" : "市");
        List<String> list4 = new ArrayList<>();
        list4.add(Objects.equals(6, policeKind) ? "街道" : "区县");
        districtTableHeadList.add(list1);
        districtTableHeadList.add(list2);
        districtTableHeadList.add(list3);
        districtTableHeadList.add(list4);

        WriteSheet districtTableSheet = EasyExcel.writerSheet(Objects.equals(6, policeKind) ? "居住地编码" : "户籍地编码")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(districtTableHeadList).build();
        excelWriter.write(districtDataList, districtTableSheet);
        excelWriter.finish();
    }

    /**
     * 遍历
     *
     * @param label 标签
     * @param list  结果集
     * @param path  路径
     */
    public static void traverseLabel(LabelVO label, List<List<String>> list, List<String> path) {
        path.add(label.getName());
        ArrayList<String> dataList = new ArrayList<>(path);
        dataList.add(0, label.getId().toString());
        list.add(dataList);
        List<LabelVO> children = label.getChildren();
        if (!children.isEmpty()) {
            for (LabelVO child : children) {
                traverseLabel(child, list, new ArrayList<>(path));
            }
        }
    }

    /**
     * 遍历地域
     *
     * @param dto  地域
     * @param list 结果集
     * @param path 路径
     */
    public static void traverseDistrict(DistrictDto dto, List<List<String>> list, List<String> path) {
        path.add(dto.getName());
        ArrayList<String> dataList = new ArrayList<>(path);
        dataList.add(0, dto.getCode());
        list.add(dataList);
        List<DistrictDto> children = dto.getChildren();
        if (!children.isEmpty()) {
            for (DistrictDto child : children) {
                traverseDistrict(child, list, new ArrayList<>(path));
            }
        }
    }

    /**
     * 导入人员
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    public ImportResultVO importPerson(ImportVO vo) {
        final MultipartFile excel = vo.getFile();
        PersonReadExcelListener listener = PersonReadExcelListener.builder()
                .personMapper(personMapper)
                .profilePersonPoliceControlMapper(profilePersonPoliceControlMapper)
                .profileVirtualIdentityMapper(profileVirtualIdentityMapper)
                .profileVehicleMapper(profileVehicleMapper)
                .dictService(dictService)
                .isGroup(false)
                .permissionService(permissionService)
                .currentUser(AuthHelper.getNotNullSimpleUser())
                .repeatStrategy(vo.getRepeatStrategy())
                .groupMapper(groupMapper)
                .operationLogService(operationLogService)
                .build();
        return importProfile(excel, listener);
    }

    /**
     * 导入档案
     *
     * @param excel    导入文件
     * @param listener easyExcel监听器
     * @param aClass   要转换成的实体
     * @return 导入结果
     */
    public ImportResultVO newImportProfile(MultipartFile excel, BaseReadExcelListener listener, Class aClass) {
        try (InputStream inputStream = excel.getInputStream()) {
            EasyExcelFactory.read(inputStream, listener)
                    .head(aClass)//要转换程的实体
                    .sheet()
                    .headRowNumber(1)//头占用了几行
                    .doRead();
            int allCount = listener.getAllCount();
            int successCount = listener.getSuccessCount();
            return new ImportResultVO(allCount, successCount, allCount - successCount, listener.getImportFailInfo());
        } catch (IOException e) {
            throw new SystemException(
                    String.format("import person fail! fileName = %s", excel.getOriginalFilename()), e);
        }
    }

    /**
     * 导入档案
     *
     * @param excel    导入文件
     * @param listener easyExcel监听器
     * @return 导入结果
     */
    public ImportResultVO importProfile(MultipartFile excel, PersonReadExcelListener listener) {
        try (InputStream inputStream = excel.getInputStream()) {
            EasyExcelFactory.read(inputStream, listener).sheet().headRowNumber(1).doRead();
            // 记录失败
            int allCount = listener.getAnalysisCount().get();
            int successCount = listener.getSuccess().size();
            return new ImportResultVO(allCount, successCount, allCount - successCount, listener.getFailResult());
        } catch (IOException e) {
            throw new SystemException(
                    String.format("import person fail! fileName = %s", excel.getOriginalFilename()), e);
        }
    }

    private FileInfoVO recordResult(Map<Integer, Map<Integer, String>> failRecordRows,
                                    Map<Integer, PersonImportFieldEnum> columnMap, List<ImportFailVO> failResult) throws IOException {
        if (failRecordRows.size() > 0) {
            List<List<String>> allHead = columnMap.entrySet()
                    .stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> {
                        List<String> head = new LinkedList<>();
                        head.add(entry.getValue().getCnName());
                        return head;
                    })
                    .collect(Collectors.toList());
            allHead.add(allHead.size(), Collections.singletonList("失败原因"));

            List<List<String>> data = new ArrayList<>();
            failRecordRows.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey()).forEach(failRecord -> {
                        List<String> collect = columnMap.entrySet()
                                .stream()
                                .sorted(Map.Entry.comparingByKey())
                                .map(entry -> failRecord.getValue().getOrDefault(entry.getKey(), ""))
                                .collect(Collectors.toList());
                        List<String> failResultDescList = failResult.stream()
                                .filter(item -> item.getRowIndex().equals(failRecord.getKey())).map(ImportFailVO::getDesc).collect(
                                        Collectors.toList());
                        StringBuilder failResultDesc = new StringBuilder();
                        for (int i = 0; i < failResultDescList.size(); i++) {
                            failResultDesc.append(i + 1).append("、").append(failResultDescList.get(i)).append("\n");
                        }
                        collect.add(collect.size(), failResultDesc.toString());
                        data.add(collect);
                    });
            if (Files.notExists(TEMP_DIR_PATH) || !Files.isDirectory(TEMP_DIR_PATH)) {
                // 没有临时文件存储文件夹，则创建一个
                Files.createDirectory(TEMP_DIR_PATH);
            }

            String filePath = System.getProperty("java.io.tmpdir") + "/" + "temp.xlsx";
            EasyExcel.write(filePath).head(allHead).sheet("Sheet1").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(data);

            File file = new File(filePath);
            FileInputStream input = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "application/vnd.ms-excel",
                    input);

            file.delete();
            return ossService.upload(multipartFile, false);
        } else {
            return null;
        }

    }

    /**
     * 导入群体档案
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    public ImportResultVO importGroup(ImportVO vo) {
        final MultipartFile excel = vo.getFile();
        GroupReadExcelListener listener = GroupReadExcelListener.builder()
                .groupMapper(groupMapper)
                .repeatStrategy(vo.getRepeatStrategy())
                .groupConverter(groupConverter)
                .profileGroupGovControlMapper(profileGroupGovControlMapper)
                .profileGroupPoliceControlMapper(profileGroupPoliceControlMapper)
                .profileSensitiveTimeMapper(profileSensitiveTimeMapper)
                .personGroupRelationMapper(personGroupRelationMapper)
                .personMapper(personMapper)
                .operationLogService(operationLogService)
                .build();
        return newImportProfile(excel, listener, GroupModel.class);
    }

    /**
     * 导入事件档案
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    public ImportResultVO importEvent(ImportVO vo) {
        final MultipartFile excel = vo.getFile();
        EventReadExcelListener listener = EventReadExcelListener.builder()
                .eventMapper(eventMapper)
                .repeatStrategy(vo.getRepeatStrategy())
                .eventConverter(eventConverter)
                .clueMapper(clueMapper)
                .groupMapper(groupMapper)
                .personMapper(personMapper)
                .eventClueRelationMapper(eventClueRelationMapper)
                .eventGroupRelationMapper(eventGroupRelationMapper)
                .eventPersonRelationMapper(eventPersonRelationMapper)
                .personRelationService(personRelationService)
                .clueRelationService(clueRelationService)
                .groupRelationService(groupRelationService)
                .operationLogService(operationLogService)
                .build();
        return newImportProfile(excel, listener, EventModel.class);
    }

    /**
     * 导入线索档案
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    public ImportResultVO importClue(ImportVO vo) {
        final MultipartFile excel = vo.getFile();
        ClueReadExcelListener listener = ClueReadExcelListener.builder()
                .clueMapper(clueMapper)
                .permissionService(permissionService)
                .personClueRelationMapper(personClueRelationMapper)
                .groupClueRelationMapper(groupClueRelationMapper)
                .personMapper(personMapper)
                .groupMapper(groupMapper)
                .repeatStrategy(vo.getRepeatStrategy())
                .build();
        return newImportProfile(excel, listener, ClueModel.class);
    }

    /**
     * 导入物品档案
     *
     * @param vo 导入参数
     * @return 导入结果
     */
    public ImportResultVO importGoods(ImportVO vo) {
        final MultipartFile excel = vo.getFile();
        GoodsReadExcelListener listener = GoodsReadExcelListener.builder()
                .goodsMapper(goodsMapper)
                .permissionService(permissionService)
                .goodsRiskControlMapper(goodsRiskControlMapper)
                .goodsApprovalService(goodsApprovalService)
                .goodsEditApprovalService(goodsEditApprovalService)
                .goodsOperationLogMapper(goodsOperationLogMapper)
                .repeatStrategy(vo.getRepeatStrategy())
                .dictService(dictService)
                .failData(new ArrayList<>())
                .build();
        return newImportProfile(excel, listener, GoodsModel.class);
    }

    /**
     * 下载线索导入模板
     *
     * @param response 响应
     * @throws IOException io异常
     */
    public void downloadClueTemplate(HttpServletResponse response) throws IOException {
        final InputStream in = this.getClass().getResourceAsStream("/template/importClue.xlsx");

        // 创建ByteArrayOutputStream以存储修改后的Excel文件
        final ByteArrayOutputStream out = new ByteArrayOutputStream();

        //处理线索标签
        List<List<String>> clueLabelDataList = new ArrayList<>();
        List<LabelVO> clue = labelService.getLabelListPermission("clue");
        for (LabelVO labelVO : clue) {
            traverseLabel(labelVO, clueLabelDataList, new ArrayList<>());
        }
        // 创建标签表头
        List<List<String>> tableHeadList = new ArrayList<>();
        List<String> id = new ArrayList<>();
        id.add("编号");
        tableHeadList.add(id);
        List<String> maxList = Collections.max(clueLabelDataList, Comparator.comparing(List::size));
        for (int i = 0; i < maxList.size() - 1; i++) {
            List<String> head = new ArrayList<>();
            head.add((i + 1) + "级标签");
            tableHeadList.add(head);
        }
        WriteSheet tableSheet = EasyExcel.writerSheet("线索类别")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(tableHeadList).build();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.write(clueLabelDataList, tableSheet);
        excelWriter.finish();

        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("线索档案导入模板.xlsx", StandardCharsets.UTF_8));

        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }


    /**
     * 现在事件档案批量导入相关人员模板
     *
     * @param response response
     * @throws IOException 异常
     */
    public void downLoadRelatedPersonTemplate(HttpServletResponse response) throws IOException {
        String eventTemplateName = BeanFactoryHolder.getEnv().getProperty("profile.import.template.event",
                "eventRelatedPersonImport.xlsx");
        final InputStream in = this.getClass().getResourceAsStream("/template/" + eventTemplateName);

        // 创建ByteArrayOutputStream以存储修改后的Excel文件
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        //处理事件标签
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.finish();

        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("事件档案比中人员批量导入模板.xlsx",
                StandardCharsets.UTF_8));

        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }

    /**
     * 批量导入事件人员相关信息
     *
     * @param vo vo
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ZgEventBzPersonVO> importRelatedPerson(ImportVO vo) throws IOException {
        InputStream inputStream = vo.getFile().getInputStream();
        List<EventRelatedPersonModel> importPersonList = EasyExcel.read(inputStream).head(EventRelatedPersonModel.class)
                .sheet().headRowNumber(1).doReadSync();
        List<String> idCards = importPersonList.stream().map(EventRelatedPersonModel::getIdCard).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idCards)){
            return new ArrayList<>();
        }
        //获取所有人员的户籍地code,新建或更新人档时需要根据地址匹配districtCode
        List<String> districtCodes = importPersonList.stream().map(EventRelatedPersonModel::getAddress)
                .collect(Collectors.toList());
        List<DistrictDto> districtList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(districtCodes)){
            districtList = dictService.getByDistrictNames(districtCodes);
        }
        Map<String,String> districtNameToCodeMap = CollectionUtils.isEmpty(districtList) ? new HashMap<>()
                : districtList.stream().collect(Collectors.toMap(DistrictDto::getName,DistrictDto::getCode));
        //已建档人员(包含删除和未删除的人)
        List<Person> personList = personMapper.selectList(new QueryWrapper<Person>().in("id_number", idCards));
        //有归属警种的人，若字段为空，需要按照导入的数据来
        List<Person> inUsePersonList = CollectionUtils.isEmpty(personList) ? new ArrayList<>()
                : personList.stream().filter(e -> !e.getDeleted() && CollectionUtils.isNotEmpty(e.getPoliceKind()))
                .collect(Collectors.toList());
        updateInUsePersonInfo(inUsePersonList,importPersonList,districtNameToCodeMap);
        //删除的人员则设置状态为未删除，同事设置policeKind为空
        List<Person> deletedPerson = CollectionUtils.isEmpty(personList) ? new ArrayList<>()
                : personList.stream().filter(e -> e.getDeleted()).collect(Collectors.toList());
        updatePersonInfo(deletedPerson,importPersonList,districtNameToCodeMap);
        //已建档但没有归属警种，更新详细地址
        List<Person> documentedPersonWithoutPoliceKind = personList.stream()
                .filter(e -> !e.getDeleted() && CollectionUtils.isEmpty(e.getPoliceKind()))
                .collect(Collectors.toList());
        updatePersonInfo(documentedPersonWithoutPoliceKind,importPersonList,districtNameToCodeMap);
        //获取profile_person表中已经有的人员身份证
        List<String> documentedIdCards = CollectionUtils.isEmpty(personList) ? new ArrayList<>()
                : personList.stream().map(Person::getIdNumber).collect(Collectors.toList());
        //人档数据库种没数据的身份证信息需要新增
        List<EventRelatedPersonModel> unDocumentedPerson = importPersonList.stream()
                .filter(e -> !documentedIdCards.contains(e.getIdCard()))
                .collect(Collectors.toList());
        //新建档案人员
        List<Person> newDocumentedPerson = saveUnDocumentedPerson(unDocumentedPerson,districtNameToCodeMap);
        //未设置警种信息的人档
        documentedPersonWithoutPoliceKind.addAll(newDocumentedPerson);
        documentedPersonWithoutPoliceKind.addAll(deletedPerson);
        //获取所有人员标签
        List<Label> labels = labelMapper.selectList(new QueryWrapper<Label>().eq("module", "person"));
        labels = CollectionUtils.isEmpty(labels) ? new ArrayList<>() : labels;
        List<ZgEventBzPersonVO> result = new ArrayList<>();
        Map<String,String> districtCodeToNameMap = CollectionUtils.isEmpty(districtList) ? new HashMap<>()
                : districtList.stream().collect(Collectors.toMap(DistrictDto::getCode,DistrictDto::getName));
        result.addAll(buildZgBzPersonVo(inUsePersonList, ZgEventConstant.MATCHED, districtCodeToNameMap,labels));
        result.addAll(buildZgBzPersonVo(documentedPersonWithoutPoliceKind, ZgEventConstant.UN_MATCHED,
                districtCodeToNameMap, labels));
        return result;
    }

    private void updateInUsePersonInfo(List<Person> inUsePersonList, List<EventRelatedPersonModel> importPersonList,
                                       Map<String, String> districtNameToCodeMap) {
        Map<String, EventRelatedPersonModel> registeredResidenceDetailMap = importPersonList.stream().collect(Collectors.toMap(EventRelatedPersonModel::getIdCard,
                e->e,(e1,e2)->e1));
        if (CollectionUtils.isNotEmpty(inUsePersonList)){
            inUsePersonList.forEach(e->{
                EventRelatedPersonModel eventRelatedPersonModel = registeredResidenceDetailMap.get(e.getIdNumber());
                eventRelatedPersonModel = Objects.nonNull(eventRelatedPersonModel) ? eventRelatedPersonModel
                        : new EventRelatedPersonModel();
                e.setRegisteredResidenceDetail(StringUtils.isEmpty(e.getRegisteredResidenceDetail())
                        ? eventRelatedPersonModel.getAddressDetail() : e.getRegisteredResidenceDetail());
                e.setRegisteredResidence(StringUtils.isEmpty(e.getRegisteredResidence())
                        ? districtNameToCodeMap.get(eventRelatedPersonModel.getAddress()) : e.getRegisteredResidence());
            });
            personMpService.updateBatchById(inUsePersonList);
        }
    }

    private void updatePersonInfo(List<Person> deletedPerson, List<EventRelatedPersonModel> importPersonList,
                                  Map<String,String> districtNameToCodeMap) {
        Map<String, EventRelatedPersonModel> registeredResidenceDetailMap = importPersonList.stream().collect(Collectors.toMap(EventRelatedPersonModel::getIdCard,
                e->e,(e1,e2)->e1));
        if (CollectionUtils.isNotEmpty(deletedPerson)){
            deletedPerson.forEach(e->{
                e.setDeleted(false);
                e.setPoliceKind(new ArrayList<>());
                EventRelatedPersonModel eventRelatedPersonModel = registeredResidenceDetailMap.get(e.getIdNumber());
                e.setRegisteredResidenceDetail(Objects.nonNull(eventRelatedPersonModel)
                        ? eventRelatedPersonModel.getAddressDetail() : null);
                e.setRegisteredResidence(Objects.nonNull(eventRelatedPersonModel)
                        ? districtNameToCodeMap.get(eventRelatedPersonModel.getAddress()) : null);
            });
            personMpService.updateBatchById(deletedPerson);
        }
    }

    private Collection<ZgEventBzPersonVO> buildZgBzPersonVo(List<Person> personList, Integer recordStatus,
                                                            Map<String,String> districtCodeToNameMap,List<Label> labels) {
        List<ZgEventBzPersonVO> voList = new ArrayList<>();
        for (Person person : personList) {
            ZgEventBzPersonVO vo = new ZgEventBzPersonVO();
            vo.setPersonId(person.getId());
            vo.setIdNumber(person.getIdNumber());
            vo.setName(person.getName());
            vo.setRecordStatus(recordStatus);
            vo.setRecordStatusName(ZgPersonMatchEnum.codeOf(recordStatus));
            vo.setRegisteredResidence(person.getRegisteredResidence());
            vo.setRegisteredResidenceDetail(person.getRegisteredResidenceDetail());
            vo.setRegisteredResidenceName(districtCodeToNameMap.get(person.getRegisteredResidence()));
            List<String> personLabelNames = CollectionUtils.isEmpty(person.getPersonLabel()) ? new ArrayList<>()
                    : labels.stream().filter(e->person.getPersonLabel().contains(e.getId())).map(Label::getName)
                    .collect(Collectors.toList());
            vo.setPersonLabelNames(personLabelNames);
            String personLabel = CollectionUtils.isEmpty(person.getPersonLabel()) ? ""
                    : "[" + person.getPersonLabel().stream().map(String::valueOf)
                    .collect(Collectors.joining(",")) + "]";
            vo.setPersonLabel(personLabel);
            voList.add(vo);
        }
        return voList;
    }

    private List<Person> saveUnDocumentedPerson(List<EventRelatedPersonModel> unDocumentedPerson,
                                                Map<String,String> districtNameMap) {
        List<Person> list = new ArrayList<>();
        for (EventRelatedPersonModel documentedPerson : unDocumentedPerson) {
            Person person = new Person();
            person.setIdType(1);
            person.setIdNumber(documentedPerson.getIdCard());
            person.setName(documentedPerson.getName());
            person.setRegisteredResidence(districtNameMap.get(documentedPerson.getAddress()));
            person.setRegisteredResidenceDetail(documentedPerson.getAddressDetail());
            list.add(person);
        }
        personMpService.saveBatch(list);
        return list;
    }

    /**
     * 下载群体导入模板
     *
     * @param response   response
     * @param policeKind policeKind
     * @throws Exception exception
     */
    public void downloadGroupTemplateV2(HttpServletResponse response, Integer policeKind) throws Exception {
        //根据警种获取导入模板
        String templateName = getTemplateName(policeKind);
        final InputStream in = this.getClass().getResourceAsStream(templateName);
        // 创建ByteArrayOutputStream以存储修改后的Excel文件
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        List<List<String>> groupLabelDataList = new ArrayList<>();
        List<LabelVO> group = labelService.getLabelListPermission("group");
        for (LabelVO labelVO : group) {
            traverseLabel(labelVO, groupLabelDataList, new ArrayList<>());
        }
        // 创建标签表头
        List<List<String>> tableHeadList = new ArrayList<>();
        List<String> id = new ArrayList<>();
        id.add("编号");
        tableHeadList.add(id);
        List<String> maxList = Collections.max(groupLabelDataList, Comparator.comparing(List::size));
        for (int i = 0; i < maxList.size() - 1; i++) {
            List<String> head = new ArrayList<>();
            head.add((i + 1) + "级标签");
            tableHeadList.add(head);
        }
        WriteSheet tableSheet = EasyExcel.writerSheet("群体类别")
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(tableHeadList).build();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).build();
        excelWriter.write(groupLabelDataList, tableSheet);
        excelWriter.finish();
        final byte[] modifiedBytes = out.toByteArray();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 构建文件名
        String policeKindName = PoliceKindEnum.codeOf(policeKind).getName();
        String fileName = "群体档案导入模板-" + policeKindName + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        OutputStream responseOut = response.getOutputStream();
        responseOut.write(modifiedBytes);
        responseOut.flush();
        responseOut.close();
    }


    /**
     * xx
     *
     * @param vo vo
     * @return result
     */
    public ImportResultVO importGroupV2(ImportVO vo) {
        final MultipartFile excel = vo.getFile();
        switch (vo.getPoliceKind().intValue()) {
            case 3:
                JzGroupReadExcelListener jzListener = JzGroupReadExcelListener.builder()
                        .groupMapper(groupMapper)
                        .repeatStrategy(vo.getRepeatStrategy())
                        .policeKind(Math.toIntExact(vo.getPoliceKind()))
                        .groupConverter(jzGroupConverter)
                        .profileGroupGovControlMapper(profileGroupGovControlMapper)
                        .profileGroupPoliceControlMapper(profileGroupPoliceControlMapper)
                        .groupRiskOtherMapper(groupRiskOtherMapper)
                        .personGroupRelationMapper(personGroupRelationMapper)
                        .personMapper(personMapper)
                        .operationLogService(operationLogService)
                        .build();
                return newImportProfile(excel, jzListener, JzGroupModel.class);
            case 4:
                ZaGroupReadExcelListener zaListener = ZaGroupReadExcelListener.builder()
                        .groupMapper(groupMapper)
                        .repeatStrategy(vo.getRepeatStrategy())
                        .policeKind(Math.toIntExact(vo.getPoliceKind()))
                        .groupConverter(zaGroupConverter)
                        .profileGroupPoliceControlMapper(profileGroupPoliceControlMapper)
                        .groupDzControlEntityMapper(groupDzControlEntityMapper)
                        .groupRiskOtherMapper(groupRiskOtherMapper)
                        .personGroupRelationMapper(personGroupRelationMapper)
                        .personMapper(personMapper)
                        .operationLogService(operationLogService)
                        .build();
                return newImportProfile(excel, zaListener, ZaGroupModel.class);
            case 99:
                QtGroupReadExcelListener qtListener = QtGroupReadExcelListener.builder()
                        .groupMapper(groupMapper)
                        .repeatStrategy(vo.getRepeatStrategy())
                        .policeKind(Math.toIntExact(vo.getPoliceKind()))
                        .groupConverter(qtGroupConverter)
                        .profileGroupGovControlMapper(profileGroupGovControlMapper)
                        .groupRiskOtherMapper(groupRiskOtherMapper)
                        .profileGroupPoliceControlMapper(profileGroupPoliceControlMapper)
                        .profileSensitiveTimeMapper(profileSensitiveTimeMapper)
                        .personGroupRelationMapper(personGroupRelationMapper)
                        .personMapper(personMapper)
                        .operationLogService(operationLogService)
                        .build();
                return newImportProfile(excel, qtListener, QtGroupModel.class);
            default:
                throw new TRSException("群体没有该警种类型");
        }
    }


    private static String getTemplateName(Integer policeKind) {
        switch (policeKind) {
            case 3:
                return "/template/jzImportGroup.xlsx";
            case 4:
                return "/template/zaImportGroup.xlsx";
            case 99:
            default:
                return "/template/qtImportGroup.xlsx";
        }
    }
}
