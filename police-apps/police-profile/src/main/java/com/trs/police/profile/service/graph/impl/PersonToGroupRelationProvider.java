package com.trs.police.profile.service.graph.impl;

import com.trs.police.profile.domain.dto.graph.GraphQueryDTO;
import com.trs.police.profile.domain.entity.Group;
import com.trs.police.profile.domain.entity.PersonGroupRelation;
import com.trs.police.profile.domain.enums.NodeTypeEnum;
import com.trs.police.profile.domain.enums.RelationTypeEnum;
import com.trs.police.profile.domain.vo.PersonGroupVO;
import com.trs.police.profile.domain.vo.graph.GraphLinkVO;
import com.trs.police.profile.domain.vo.graph.GraphNodeVO;
import com.trs.police.profile.factory.NodeFactory;
import com.trs.police.profile.factory.RelationFactory;
import com.trs.police.profile.mapper.PersonGroupRelationMapper;
import com.trs.police.profile.service.graph.RelationProvider;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 人员到群体关系提供者
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Component
@RequiredArgsConstructor
public class PersonToGroupRelationProvider implements RelationProvider {

    private final PersonGroupRelationMapper personGroupRelationMapper;

    @Override
    public String getSourceNodeType() {
        return NodeTypeEnum.PERSON.getCode();
    }

    @Override
    public RelationResult queryRelations(String sourceNodeId, GraphQueryDTO queryDTO) {
        // 检查是否需要过滤节点类型
        if (CollectionUtils.isNotEmpty(queryDTO.getNodeTypes())
                && !queryDTO.getNodeTypes().contains(NodeTypeEnum.GROUP.getName())
                && !queryDTO.getNodeTypes().contains(NodeTypeEnum.GROUP.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 检查是否需要过滤关系类型
        if (CollectionUtils.isNotEmpty(queryDTO.getRelationTypes())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.PERSON_GROUP.getName())
                && !queryDTO.getRelationTypes().contains(RelationTypeEnum.PERSON_GROUP.getCode())) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        // 查询人员-群体关系
        List<PersonGroupVO> personGroupList = personGroupRelationMapper.getPersonGroupList(
                Long.valueOf(sourceNodeId), queryDTO.getPoliceKind());

        if (CollectionUtils.isEmpty(personGroupList)) {
            return new RelationResult(new ArrayList<>(), new ArrayList<>());
        }

        List<GraphLinkVO<?>> links = new ArrayList<>();
        List<GraphNodeVO<?>> targetNodes = new ArrayList<>();

        // 构建关系和节点
        for (PersonGroupVO personGroupVO : personGroupList) {
            // 创建群体节点
            Group group = new Group();
            group.setId(personGroupVO.getId());
            group.setName(personGroupVO.getName());

            GraphNodeVO<?> groupNode = NodeFactory.createGroupNode(group, NodeTypeEnum.GROUP);
            targetNodes.add(groupNode);

            // 创建人员-群体关系
            PersonGroupRelation relation = new PersonGroupRelation();
            relation.setPersonId(Long.valueOf(sourceNodeId));
            relation.setGroupId(group.getId());
            relation.setActivityLevel(personGroupVO.getActivityLevel());
            relation.setGroupWork(personGroupVO.getGroupWork());
            relation.setPoliceKind(queryDTO.getPoliceKind() != null ? queryDTO.getPoliceKind().intValue() : null);

            GraphLinkVO<?> link = RelationFactory.createPersonToGroupRelation(
                    sourceNodeId, group.getId().toString(), relation);
            links.add(link);
        }

        return new RelationResult(links, targetNodes);
    }
}