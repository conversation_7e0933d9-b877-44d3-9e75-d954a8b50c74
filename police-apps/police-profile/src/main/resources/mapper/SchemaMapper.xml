<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.profile.schema.mapper.SchemaMapper">

    <!-- 档案数据权限 -->
    <sql id="profilePermissionCondition">
        <if test="null != profileSearchPermission and '' != profileSearchPermission.dbFileName">
            and
            json_overlaps(a.${profileSearchPermission.dbFileName}, #{profileSearchPermission.labelIdStr})
        </if>
    </sql>

    <insert id="doInsert">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="statement.id">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into ${statement.table}
        <foreach collection="statement.fields" separator="," item="field" open="(" close=")">
            ${field.column}
        </foreach>
        values
        <foreach collection="statement.fields" separator="," item="field" open="(" close=")">
            #{field.value}
        </foreach>
    </insert>

    <update id="doUpdate">
        update ${statement.table} set
        <foreach collection="statement.fields" separator="," item="field" open="" close="">
            ${field.column} = #{field.value}
        </foreach>
        <where>
            ${statement.keyName} = #{statement.keyValue}
        </where>
    </update>

    <update id="doDelete">
        delete
        from ${statement.table}
        where ${statement.keyName} = #{statement.keyValue}
        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
            <foreach collection="databaseRelation.extendCondition" item="condition">
                and ${condition.column} = ${condition.value}
            </foreach>
        </if>
    </update>

    <delete id="doDeleteWithExcludedIds">
        delete
        from ${databaseRelation.table}
        where ${databaseRelation.column} = ${relatedId}
        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
            <foreach collection="databaseRelation.extendCondition" item="condition">
                and ${condition.column} = ${condition.value}
            </foreach>
        </if>
        <if test="null != excludedIds and excludedIds.size() > 0">
            and id not in
            <foreach collection="excludedIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <delete id="doFileDelete">
        delete
        from ${db.table}
        where ${db.joinTo.joinColumn} = #{id}
        and ${db.joinFrom.joinColumn} = #{relatedId}
    </delete>

    <delete id="doBatchFileDelete">
        delete
        from ${db.table}
        where ${db.joinTo.joinColumn} in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and ${db.joinFrom.joinColumn} = ${relatedId}
    </delete>

    <select id="doPageSelect" resultType="java.util.Map">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        <bind name="dataPermission" value="schema.dataPermission"/>
        <bind name="profileDataPermission" value="schema.profileDataPermission"/>
        select a.id as id,
        <foreach collection="fields" separator="," item="field">
            <choose>
                <when test="field.db.databaseRelation != null">
                    <choose>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                            <choose>
                                <when test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@SPECIAL_TABLE_SCHEMA_PROFILE_PERSON_POLICE_CONTROL">
                                    (SELECT REPLACE(GROUP_CONCAT( distinct ${field.db.column} SEPARATOR ','),'],[',',') AS result
                                    FROM ${field.db.table} WHERE ${field.db.databaseRelation.column} = a.id) as
                                    ${field.name}
                                </when>
                                <when test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@SPECIAL_TABLE_SCHEMA_PROFILE_GROUP_POLICE_CONTROL">
                                    (SELECT REPLACE(GROUP_CONCAT( distinct ${field.db.column} SEPARATOR ','),'],[',',') AS result
                                    FROM ${field.db.table} WHERE ${field.db.databaseRelation.column} = a.id) as
                                    ${field.name}
                                </when>
                                <otherwise>
                                    (select ${field.db.column} from ${field.db.table} where
                                    ${field.db.databaseRelation.column}
                                    = a.id limit 1) as ${field.name}
                                </otherwise>
                            </choose>
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                            (select ${field.db.column} from ${field.db.table} where
                            ${field.db.databaseRelation.joinFrom.joinColumn}=${relatedId} and
                            ${field.db.databaseRelation.joinTo.joinColumn}=a.id
                            <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                                <foreach collection="databaseRelation.extendCondition" item="condition">
                                    and ${condition.column} = ${condition.value}
                                </foreach>
                            </if>
                            limit 1) as ${field.name}
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@SCORE_SUM">
                            (SELECT SUM(m.${field.db.databaseRelation.joinSumTable.sumColumn}) from ${field.db.databaseRelation.joinSumTable.table} as m WHERE
                            FIND_IN_SET(m.${field.db.databaseRelation.joinSumTable.joinColumn},(select ${field.db.column} from ${field.db.table} where
                            ${field.db.databaseRelation.joinFrom.joinColumn}=${relatedId} and
                            ${field.db.databaseRelation.joinTo.joinColumn}=a.id))>0) AS ${field.name}
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                            (select ${field.db.column} from ${field.db.table} where ${field.db.databaseRelation.column}
                            = a.${field.db.databaseRelation.joinFrom.joinColumn}) as ${field.name}
                        </when>
                        <when test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@EVENT_RELATED_GROUP">
                            <if test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@EVENT_RELATED_GROUP_TABLE">
                                (SELECT REPLACE(GROUP_CONCAT( ${field.db.column} SEPARATOR ','),'],[',',')
                                FROM ${field.db.table}
                                WHERE ${field.db.databaseRelation.column} = a.id and JSON_LENGTH(g.group_label) != 0) as ${field.name}
                            </if>
                        </when>
                        <when test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATED_TWO_TABLE">
                            (select REPLACE(GROUP_CONCAT( c.${field.db.column} SEPARATOR ','),'],[',',')
                            from ${field.db.table} c, ${field.db.databaseRelation.table} b where
                            b.${field.db.databaseRelation.joinFrom.joinColumn} = c.${field.db.databaseRelation.column} and
                            b.${field.db.databaseRelation.joinTo.joinColumn}=a.id) as ${field.name}
                        </when>
                    </choose>
                </when>
                <otherwise>
                    a.${field.db.column} as ${field.name}
                </otherwise>
            </choose>
        </foreach>
        from ${table} as a
        <where>
            <!-- 从filterParams生成筛选条件 -->
            <if test="filterParams != null and filterParams.size > 0">
                <foreach collection="filterParams" item="param">
                    <bind name="field" value="schema.getField(param.key)"/>
                    <if test="param.key == 'policeKind'">
                        <if test="param.value == 4 or param.value == 3 or param.value == 99">
                            and a.police_kind is not null
                        </if>
                    </if>
                    <if test="param.key == 'ids'">
                        <if test="param.value != null and param.value.size > 0">
                            and a.id in
                            <foreach collection="param.value" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
                    <if test="field != null">
                        <choose>
                            <!-- 如果没有join则只查询本字段即可-->
                            <when test="field.db.databaseRelation == null">
                                <choose>
                                    <!-- 如果是标签则需要用path去like -->
                                    <when test="field.db.jdbcType.equals('label_id_array')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            JSON_OVERLAPS( a.${field.db.column},
                                            (
                                            SELECT JSON_ARRAYAGG( l.id )
                                            FROM t_profile_label l
                                            WHERE CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                                            ))> 0
                                        </foreach>
                                    </when>
                                    <!-- 时间范围,查全部时不加条件 -->
                                    <when test="param.type.equals('timeParams')">
                                        <if test="param.getProcessedValue().isAll() == false">
                                            AND (a.${field.db.column} >= '${param.value.beginTime}'
                                            AND a.${field.db.column} &lt;= '${param.value.endTime}')
                                        </if>
                                    </when>
                                    <!-- 如果是district需要去like -->
                                    <when test="field.db.jdbcType.equals('district')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            a.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <!-- 如果是部门id直接 in-->
                                    <when test="field.db.jdbcType.equals('dept_id')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            a.${field.db.column} in (SELECT d.id FROM t_dept d WHERE CONCAT( d.path, d.id,
                                            '-') LIKE CONCAT('%-', ${item}, '-%' ))
                                        </foreach>
                                    </when>
                                    <!-- 查询部门代码列表 -->
                                    <when test="field.db.jdbcType.equals('dept_code')">
                                        AND a.${field.db.column} in (SELECT d.code FROM t_dept d WHERE
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator=" OR " close=")">
                                            d.code like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}',
                                            '%')
                                        </foreach>
                                        )
                                    </when>
                                    <when test="field.db.jdbcType.equals('case_label_array')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            JSON_OVERLAPS( a.${field.db.column},
                                            (
                                            SELECT JSON_ARRAYAGG( l.CODE )
                                            FROM t_profile_case_label l WHERE CONCAT( l.path, l.code, '-' )
                                            LIKE CONCAT('%-',#{item},'-%' )
                                            ))> 0
                                        </foreach>
                                    </when>
                                    <!-- 如果是jwzh码表需要去like -->
                                    <when test="field.db.jdbcType.equals('jwzh')">
                                        AND left(a.${field.db.column},
                                        ${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value).length()})
                                        =
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}'
                                    </when>
                                    <when test="field.db.jdbcType.equals('array')">
                                        AND a.${field.db.column} in
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                            '${item}'
                                        </foreach>
                                    </when>
                                    <!-- 直接本字段比较 -->
                                    <otherwise>
                                        AND a.${field.db.column} = '${param.value}'
                                    </otherwise>
                                </choose>
                            </when>
                            <when test="field.db.column=='count(1)'">
                                <if test="param.value==1">
                                    and (select count(0) from ${field.db.table} b where
                                    b.${field.db.databaseRelation.column} = a.id) >0
                                </if>
                                <if test="param.value==0">
                                    and (select count(0) from ${field.db.table} b where
                                    b.${field.db.databaseRelation.column} = a.id) =0
                                </if>
                            </when>
                            <otherwise>
                                <!-- 如果是外键关联需要用子查询 -->
                                <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                                    AND EXISTS (
                                    SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                    b.${field.db.databaseRelation.column } = a.id
                                    <choose>
                                        <!-- 如果是部门编号需要用prefix去like -->
                                        <when test="field.db.jdbcType.equals('dept_code')">
                                            AND
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="OR" close=")">
                                                b.${field.db.column} like concat(
                                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                                , '%')
                                            </foreach>
                                        </when>
                                        <when test="field.db.jdbcType.equals('array')">
                                            AND b.${field.db.column} in
                                            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                                '${item}'
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            AND b.${field.db.column}='${param.value}'
                                        </otherwise>
                                    </choose>
                                    )
                                </if>
                                <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                    AND EXISTS (
                                    SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                    b.${field.db.databaseRelation.column} =
                                    a.${field.db.databaseRelation.joinFrom.joinColumn}
                                    <choose>
                                        <when test="field.db.jdbcType.equals('datetime')">
                                            AND (b.${field.db.column} >=
                                            '${param.value.beginTime}'
                                            AND b.${field.db.column} &lt; '${param.value.endTime}')
                                        </when>
                                        <when test="field.db.jdbcType.equals('stringTime')">
                                            AND (
                                            cast(b.${field.db.column} as DATETIME) >=
                                            '${param.value.beginTime}'
                                            AND cast(b.${field.db.column} as DATETIME) &lt; '${param.value.endTime}'
                                            )
                                        </when>
                                        <when test="field.db.jdbcType.equals('dept_code')">
                                            AND
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="OR" close=")">
                                                b.${field.db.column} like concat(
                                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                                , '%')
                                            </foreach>
                                        </when>
                                        <when test="field.db.jdbcType.equals('district')">
                                            AND
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="OR" close=")">
                                                b.${field.db.column} like concat(
                                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                                , '%')
                                            </foreach>
                                        </when>
                                        <!-- jwzh码表，需要用前缀匹配 -->
                                        <when test="field.db.jdbcType.equals('jwzh')">
                                            AND left(b.${field.db.column},
                                            ${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value).length()})
                                            =
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}'
                                        </when>
                                        <when test="field.db.jdbcType.equals('array')">
                                            AND b.${field.db.column} in
                                            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                                '${item}'
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            AND b.${field.db.column} = '${param.value}'
                                        </otherwise>
                                    </choose>
                                    )
                                </if>
                                <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATED_TWO_TABLE">
                                    AND EXISTS (
                                    select c.${field.db.column}
                                    from ${field.db.table} c, ${field.db.databaseRelation.table} b where
                                    b.${field.db.databaseRelation.joinFrom.joinColumn} = c.${field.db.databaseRelation.column} and
                                    b.${field.db.databaseRelation.joinTo.joinColumn}=a.id
                                    <choose>
                                        <when test="field.db.jdbcType.equals('array')">
                                            AND c.${field.db.column} in
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="," close=")">
                                                '${item}'
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            AND c.${field.db.column} = '${param.value}'
                                        </otherwise>
                                    </choose>
                                    )
                                </if>
                            </otherwise>
                        </choose>
                    </if>
                    <!-- 人员、事件、群体管控警种筛选特殊逻辑 -->
                    <if test="field == null and param.value != null and  param.value != '' and 'controlPoliceKind' == param.key and relatedId == null
                    and (table == 't_profile_group' or table == 't_profile_person' or table == 't_profile_event')">
                        <foreach collection='param.value.toString().split(",")' separator="or" item="item" open="AND (" close=")">
                            JSON_CONTAINS(a.police_kind, #{item})
                            <!-- 加上审批权限 -->
                                and JSON_CONTAINS(a.police_kind, '${item}')
                            and (
                                a.approval_detail is null
                            or (
                                JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${item}".status'), '3')
                                or JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${item}".approvalUserId'), '${permissionInfo.userId}')
                                or a.create_dept_id = #{permissionInfo.deptId}
                                )
                            )
                        </foreach>
                    </if>
                </foreach>
            </if>

            <!-- 从searchParams生成搜索条件 -->
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.getSearchValue())">
                <bind name="field" value="schema.getField(searchParams.searchField)"/>
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test="field != null">
                        <if test="field.db.databaseRelation != null">
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND a.${field.db.databaseRelation.column} in (
                                SELECT b.${field.db.databaseRelation.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column } =
                                a.${field.db.databaseRelation.joinFrom.joinColumn} and b.${field.db.column} like
                                #{pattern,jdbcType=VARCHAR}
                                )
                            </if>
                            <if test="field.db.databaseRelation.type != @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND a.${field.db.column} like #{pattern,jdbcType=VARCHAR}
                            </if>
                        </if>
                        <if test="field.db.databaseRelation == null">
                            AND a.${field.db.column} like #{pattern,jdbcType=VARCHAR}
                        </if>
                    </when>
                    <otherwise>
                        AND
                        <foreach collection="schema.searchFields" open="(" separator="OR" close=")" item="sf">
                            <bind name="f" value="schema.getField(sf.key)"/>
                            <if test="f.db.databaseRelation != null">
                                <choose>
                                    <when
                                            test="f.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                        a.${f.db.databaseRelation.column} in (
                                        SELECT b.${f.db.databaseRelation.column} from ${f.db.table} b WHERE
                                        b.${f.db.databaseRelation.column } =
                                        a.${f.db.databaseRelation.joinFrom.joinColumn} and b.${f.db.column} like
                                        #{pattern,jdbcType=VARCHAR}
                                        )
                                    </when>
                                    <otherwise>
                                        a.${f.db.column} like #{pattern,jdbcType=VARCHAR}
                                    </otherwise>
                                </choose>
                            </if>
                            <if test="f.db.databaseRelation == null">
                                a.${f.db.column} like #{pattern,jdbcType=VARCHAR}
                            </if>

                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <!-- 从module-databaseRelation和relatedId生成关联表检索条件 -->
            <if test="databaseRelation != null">
                <choose>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@JSON_ID_ARRAY">
                        AND a.id MEMBER OF ((SELECT j.${databaseRelation.column } FROM ${databaseRelation.table } j
                        WHERE j.id = ${relatedId}))
                    </when>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE ||
                                databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@SCORE_SUM">
                        AND a.${databaseRelation.joinTo.column} IN (
                        SELECT t.${databaseRelation.joinTo.column} FROM ${databaseRelation.table} r
                        JOIN ${databaseRelation.joinFrom.table} f ON f.${databaseRelation.joinFrom.column} =
                        r.${databaseRelation.joinFrom.joinColumn}
                        JOIN ${databaseRelation.joinTo.table} t ON t.${databaseRelation.joinTo.column} =
                        r.${databaseRelation.joinTo.joinColumn}
                        <if test="databaseRelation.joinFrom.primaryKey != null">
                            WHERE f.${databaseRelation.joinFrom.primaryKey}=${relatedId}
                        </if>
                        <if test="databaseRelation.joinFrom.primaryKey == null">
                            WHERE f.${databaseRelation.joinFrom.column}=${relatedId}
                        </if>
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and r.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                        )
                    </when>
                    <when test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                        and a.${databaseRelation.column} = ${relatedId}
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and a.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                    </when>
                    <when test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                        and a.${databaseRelation.column} =
                        (select ${databaseRelation.primaryColumn} from ${databaseRelation.table} t where t.id = ${relatedId})
                    </when>
                </choose>
            </if>

            <!-- 从dataPermission生成数据权限筛选条件 -->
            <if test="dataPermission != null and permissionInfo != null">
                <foreach collection="dataPermission" item="config">
                    <if test="config.level == permissionInfo.permission">
                        <choose>
                            <!-- 数据权限为本人/本部门/本地区 -->
                            <when
                                    test="permissionInfo.permission == 'SELF' or permissionInfo.permission == 'DEPT' or permissionInfo.permission == 'DISTRICT'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} = ${permissionInfo.getField(config.infoField)}
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} = '${permissionInfo.getField(config.infoField)}')
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为本部门及以下/本地区及以下 -->
                            <when
                                    test="permissionInfo.permission == 'DISTRICT_AND_CHILD' or permissionInfo.permission == 'DEPT_AND_CHILD'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                        )
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为ALL，不做处理 -->
                        </choose>
                    </if>
                </foreach>
            </if>

            <!-- 档案数据权限 （根据档案类型） -->
            <include refid="profilePermissionCondition"/>
            and a.deleted = 0
        </where>
        <if test="filterParams != null and filterParams.size > 0">
            <foreach collection="filterParams" item="param">
                <bind name="field" value="schema.getField(param.key)"/>
                <if test="field != null and field.db.jdbcType == 'eventRelatedGroup'">
                    having
                    JSON_OVERLAPS( groupLabel,
                    (
                    SELECT JSON_ARRAYAGG( l.id )
                    FROM t_profile_label l
                    WHERE
                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                             close=")">
                        CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                    </foreach>
                    ))> 0
                </if>
            </foreach>
        </if>
        <!-- 排序参数 -->
        <choose>
            <when test="sortParams != null and sortParams.sortField != null">
                <bind name="field" value="schema.getField(sortParams.sortField)"/>
                <if test="databaseRelation != null and databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@SCORE_SUM">
                    ORDER BY ${sortParams.sortField} ${sortParams.getProcessedValue()}
                </if>
                <if test="field != null and (databaseRelation == null or databaseRelation.type != @com.trs.police.profile.schema.db.DatabaseRelationType@SCORE_SUM)">
                    ORDER BY ${field.db.column} ${sortParams.getProcessedValue()}, a.id desc
                </if>
            </when>
            <otherwise>
                order by update_time desc,a.id desc
            </otherwise>
        </choose>

    </select>

    <select id="checkDataPermission" resultType="java.lang.Boolean">
        <bind name="table" value="schema.table"/>
        <bind name="dataPermission" value="schema.dataPermission"/>
        <bind name="profileDataPermission" value="schema.profileDataPermission"/>
        select count(*) from ${table} as a
        <where>
            id = #{recordId}
            <if test="dataPermission != null">
                <foreach collection="dataPermission" item="config">
                    <if test="config.level == permissionInfo.permission">
                        <choose>
                            <!-- 数据权限为本人/本部门/本地区 -->
                            <when
                                    test="permissionInfo.permission == 'SELF' or permissionInfo.permission == 'DEPT' or permissionInfo.permission == 'DISTRICT'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} = ${permissionInfo.getField(config.infoField)}
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} = '${permissionInfo.getField(config.infoField)}')
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为本部门及以下/本地区及以下 -->
                            <when test="permissionInfo.permission == 'DISTRICT_AND_CHILD' or permissionInfo.permission == 'DEPT_AND_CHILD'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                        )
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为ALL，不做处理 -->
                        </choose>
                    </if>
                </foreach>
            </if>
            <!-- 档案数据权限 （根据档案类型） -->
            <include refid="profilePermissionCondition"/>
            and a.deleted = 0
        </where>
        limit 1
    </select>

    <select id="doListSelectV2" resultType="java.util.Map">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        <bind name="extendFields" value="schema.extendFields"/>
        select a.id as id,
        <foreach collection="fields" separator="," item="field">
            <choose>
                <when test="field.db.table == null or field.db.table == '' or field.db.table.equals(table)">
                    a.${field.db.column} as ${field.name}
                </when>
                <when test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@EVENT_RELATED_GROUP">
                        <if test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@EVENT_RELATED_GROUP_TABLE">
                        (SELECT REPLACE(GROUP_CONCAT( ${field.db.column} SEPARATOR ','),'],[',',')
                        FROM ${field.db.table}
                        WHERE ${field.db.databaseRelation.column} = a.id and JSON_LENGTH(g.group_label) != 0) as ${field.name}
                    </if>
                </when>
                <otherwise>
                    <if test="field.db.databaseRelation != null and field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                        <choose>
                            <when test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@SPECIAL_TABLE_SCHEMA_PROFILE_PERSON_POLICE_CONTROL">
                                (SELECT REPLACE(GROUP_CONCAT( distinct ${field.db.column} SEPARATOR ','),'],[',',') AS result
                                FROM ${field.db.table} WHERE ${field.db.databaseRelation.column} = a.id) as
                                ${field.name}
                            </when>
                            <when test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@SPECIAL_TABLE_SCHEMA_PROFILE_GROUP_POLICE_CONTROL">
                                (SELECT REPLACE(GROUP_CONCAT( distinct ${field.db.column} SEPARATOR ','),'],[',',') AS result
                                FROM ${field.db.table} WHERE ${field.db.databaseRelation.column} = a.id) as
                                ${field.name}
                            </when>
                            <otherwise>
                                (select ${field.db.column} from ${field.db.table} where
                                ${field.db.databaseRelation.column}
                                = a.id
                                <if test="extendFields != null">
                                    <foreach collection="extendFields" item="extendField">
                                        <if test="extendField.column == 'police_kind'">
                                            and ${extendField.column} = ${extendField.value}
                                        </if>
                                    </foreach>
                                </if>
                                limit 1) as ${field.name}
                            </otherwise>
                        </choose>
                    </if>
                    <if test="field.db.databaseRelation != null and field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                        (select ${field.db.column} from ${field.db.table} where ${field.db.databaseRelation.column}
                        = a.${field.db.databaseRelation.joinFrom.joinColumn}) as ${field.name}
                    </if>
                </otherwise>
            </choose>
        </foreach>
        from ${table} as a
        <where>
            <choose>
                <when test="relation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@PRIMARY_KEY">
                    AND a.id=${recordId}
                </when>
                <when test="relation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                    AND a.${relation.column}=${recordId}
                    <if test="relation!=null and relation.extendCondition!=null and relation.extendCondition.length>0">
                        <foreach collection="relation.extendCondition" item="condition">
                            and a.${condition.column} = ${condition.value}
                        </foreach>
                    </if>
                </when>
                <when test="relation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                    AND a.id =${recordId}
                </when>
            </choose>
        </where>
        <if test="isSingle">
            limit 1;
        </if>
    </select>

    <select id="doIdSelect" resultType="java.util.Map">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        select a.id as id,
        <foreach collection="fields" separator="," item="field">
            <choose>
                <when test="field.db.databaseRelation != null">
                    <choose>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                            <choose>
                                <when test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@SPECIAL_TABLE_SCHEMA_PROFILE_PERSON_POLICE_CONTROL">
                                    (SELECT REPLACE(GROUP_CONCAT( distinct ${field.db.column} SEPARATOR ','),'],[',',') AS result
                                    FROM ${field.db.table} WHERE ${field.db.databaseRelation.column} = a.id) as
                                    ${field.name}
                                </when>
                                <when test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@SPECIAL_TABLE_SCHEMA_PROFILE_GROUP_POLICE_CONTROL">
                                    (SELECT REPLACE(GROUP_CONCAT( distinct ${field.db.column} SEPARATOR ','),'],[',',') AS result
                                    FROM ${field.db.table} WHERE ${field.db.databaseRelation.column} = a.id) as
                                    ${field.name}
                                </when>
                                <otherwise>
                                    (select ${field.db.column} from ${field.db.table} where
                                    ${field.db.databaseRelation.column}
                                    = a.id limit 1) as ${field.name}
                                </otherwise>
                            </choose>
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                            (select ${field.db.column} from ${field.db.table} where
                            ${field.db.databaseRelation.joinFrom.joinColumn}=${relatedId} and
                            ${field.db.databaseRelation.joinTo.joinColumn}=a.id
                            <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                                <foreach collection="databaseRelation.extendCondition" item="condition">
                                    and ${condition.column} = ${condition.value}
                                </foreach>
                            </if>
                            limit 1) as ${field.name}
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@SCORE_SUM">
                            (SELECT SUM(m.${field.db.databaseRelation.joinSumTable.sumColumn}) from ${field.db.databaseRelation.joinSumTable.table} as m WHERE
                            FIND_IN_SET(m.${field.db.databaseRelation.joinSumTable.joinColumn},(select ${field.db.column} from ${field.db.table} where
                            ${field.db.databaseRelation.joinFrom.joinColumn}=${relatedId} and
                            ${field.db.databaseRelation.joinTo.joinColumn}=a.id))>0) AS ${field.name}
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                            (select ${field.db.column} from ${field.db.table} where ${field.db.databaseRelation.column}
                            = a.${field.db.databaseRelation.joinFrom.joinColumn}) as ${field.name}
                        </when>
                        <when test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@EVENT_RELATED_GROUP">
                            <if test="field.db.table == @com.trs.police.profile.schema.util.DbHelper@EVENT_RELATED_GROUP_TABLE">
                                (SELECT REPLACE(GROUP_CONCAT( ${field.db.column} SEPARATOR ','),'],[',',')
                                FROM ${field.db.table}
                                WHERE ${field.db.databaseRelation.column} = a.id and JSON_LENGTH(g.group_label) != 0) as ${field.name}
                            </if>
                        </when>
                        <when test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATED_TWO_TABLE">
                            (select REPLACE(GROUP_CONCAT( c.${field.db.column} SEPARATOR ','),'],[',',')
                            from ${field.db.table} c, ${field.db.databaseRelation.table} b where
                            b.${field.db.databaseRelation.joinFrom.joinColumn} = c.${field.db.databaseRelation.column} and
                            b.${field.db.databaseRelation.joinTo.joinColumn}=a.id) as ${field.name}
                        </when>
                        <otherwise>
                            (select ${field.db.column} from ${field.db.table} where
                            ${field.db.databaseRelation.column}
                            = a.id) as ${field.name}
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    a.${field.db.column} as ${field.name}
                </otherwise>
            </choose>
        </foreach>
        from ${table} as a
        <where>
            a.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="doListSelect" resultType="java.util.Map">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        select a.id as id,
        <foreach collection="fields" separator="," item="field">
            <choose>
                <when test="field.db.databaseRelation != null">
                    <choose>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                            (select ${field.db.column} from ${field.db.table} where ${field.db.databaseRelation.column}
                            = a.id limit 1) as ${field.name}
                        </when>
                        <when
                                test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                            (select ${field.db.column} from ${field.db.table} where
                            ${field.db.databaseRelation.joinFrom.joinColumn}=${relatedId} and
                            ${field.db.databaseRelation.joinTo.joinColumn}=a.id
                            <if test="relation!=null and relation.extendCondition!=null and relation.extendCondition.length>0">
                                <foreach collection="relation.extendCondition" item="condition">
                                    and ${condition.column} = ${condition.value}
                                </foreach>
                            </if>
                            limit 1) as ${field.name}
                        </when>
                    </choose>
                </when>
                <otherwise>
                    a.${field.db.column} as ${field.name}
                </otherwise>
            </choose>
        </foreach>
        from ${table} as a
        <where>
            <choose>
                <when test="relation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                    AND a.${relation.column}=${relatedId}
                    <if test="relation!=null and relation.extendCondition!=null and relation.extendCondition.length>0">
                        <foreach collection="relation.extendCondition" item="condition">
                            and ${condition.column} = ${condition.value}
                        </foreach>
                    </if>
                </when>
                <when test="relation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@JSON_ID_ARRAY">
                    AND a.id MEMBER OF ((SELECT j.${relation.column } FROM ${relation.table } j WHERE j.id =
                    ${relatedId}))
                </when>
                <when test="relation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                    AND a.${relation.joinTo.column} IN (
                    SELECT t.${relation.joinTo.column} FROM ${relation.table} r
                    JOIN ${relation.joinFrom.table} f ON f.${relation.joinFrom.column} =
                    r.${relation.joinFrom.joinColumn}
                    JOIN ${relation.joinTo.table} t ON t.${relation.joinTo.column} = r.${relation.joinTo.joinColumn}
                    <if test="relation.joinFrom.primaryKey != null">
                        WHERE f.${relation.joinFrom.primaryKey}=${relatedId}
                    </if>
                    <if test="relation.joinFrom.primaryKey == null">
                        WHERE f.${relation.joinFrom.column}=${relatedId}
                    </if>
                    <if test="relation!=null and relation.extendCondition!=null and relation.extendCondition.length>0">
                        <foreach collection="relation.extendCondition" item="condition">
                            and r.${condition.column} = ${condition.value}
                        </foreach>
                    </if>
                    )
                </when>
            </choose>
            and a.deleted = 0
        </where>
    </select>

    <select id="doFileSelect" resultType="com.trs.police.profile.schema.vo.DynamicFileDto">
        select a.*
        from t_file_info a
        <if test="db.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
            left join ${db.table} b on b.${db.joinTo.joinColumn}=a.id
            where b.${db.joinFrom.joinColumn}=#{relatedId}
        </if>
    </select>
    <update id="doBatchUpdate">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        <bind name="dataPermission" value="schema.dataPermission"/>
        <bind name="profileDataPermission" value="schema.profileDataPermission"/>
        update ${table} a set
        a.deleted=1,
        a.update_user_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().id},
        a.update_dept_id =${@com.trs.police.common.core.utils.AuthHelper@getCurrentUser().dept.id},
        a.update_time= CURRENT_TIMESTAMP()
        <where>
            <!-- 从filterParams生成筛选条件 -->
            <foreach collection="filterParams" item="param">
                <bind name="field" value="schema.getField(param.key)"/>
                <if test="field != null">
                    <choose>
                        <!-- 如果没有join则只查询本字段即可-->
                        <when test="field.db.databaseRelation == null">
                            <choose>
                                <!-- 如果是标签则需要用path去like -->
                                <when test="field.db.jdbcType.equals('label_id_array')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        JSON_OVERLAPS( a.${field.db.column},
                                        (
                                        SELECT JSON_ARRAYAGG( l.id )
                                        FROM t_profile_label l
                                        WHERE CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                                        ))> 0
                                    </foreach>
                                </when>
                                <!-- 时间范围,查全部时不加条件 -->
                                <when test="param.type.equals('timeParams')">
                                    <if test="param.getProcessedValue().isAll() == false">
                                        AND (a.${field.db.column} >= '${param.value.beginTime}'
                                        AND a.${field.db.column} &lt;= '${param.value.endTime}')
                                    </if>
                                </when>
                                <!-- 如果是district需要去like -->
                                <when test="field.db.jdbcType.equals('district')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        a.${field.db.column} like concat(
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                        , '%')
                                    </foreach>
                                </when>
                                <!-- 如果是部门id直接 in-->
                                <when test="field.db.jdbcType.equals('dept_id')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        a.${field.db.column} in (SELECT d.id FROM t_dept d WHERE CONCAT( d.path, d.id,
                                        '-') LIKE CONCAT('%-', ${item}, '-%' ))
                                    </foreach>
                                </when>
                                <!-- 查询部门代码列表 -->
                                <when test="field.db.jdbcType.equals('dept_code')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator=","
                                             close=")">
                                        a.${field.db.column} in (SELECT d.code FROM t_dept d WHERE d.code like concat(
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                        , '%'))
                                    </foreach>
                                </when>
                                <when test="field.db.jdbcType.equals('case_label_array')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        JSON_OVERLAPS( a.${field.db.column},
                                        (
                                        SELECT JSON_ARRAYAGG( l.CODE )
                                        FROM t_profile_case_label l WHERE CONCAT( l.path, l.code, '-' )
                                        LIKE CONCAT('%-',#{item},'-%' )
                                        ))> 0
                                    </foreach>
                                </when>
                                <!-- 如果是jwzh码表需要去like -->
                                <when test="field.db.jdbcType.equals('jwzh')">
                                    AND a.${field.db.column} like concat(
                                    '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}',
                                    '%')
                                </when>
                                <when test="field.db.jdbcType.equals('array')">
                                    AND a.${field.db.column} in
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                        '${item}'
                                    </foreach>
                                </when>
                                <!-- 直接本字段比较 -->
                                <otherwise>
                                    AND a.${field.db.column} = '${param.value}'
                                </otherwise>
                            </choose>
                        </when>
                        <when test="field.db.column=='count(1)'">
                            <if test="param.value==1">
                                and (select count(0) from ${field.db.table} b where
                                b.${field.db.databaseRelation.column} = a.id) >0
                            </if>
                            <if test="param.value==0">
                                and (select count(0) from ${field.db.table} b where
                                b.${field.db.databaseRelation.column} = a.id) =0
                            </if>
                        </when>
                        <otherwise>
                            <!-- 如果是外键关联需要用子查询 -->
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                                AND EXISTS (
                                SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column } = a.id
                                <choose>
                                    <!-- 如果是部门编号需要用prefix去like -->
                                    <when test="field.db.jdbcType.equals('dept_code')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="OR" close=")">
                                            b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND b.${field.db.column}='${param.value}'
                                    </otherwise>
                                </choose>
                                )
                            </if>
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND EXISTS (
                                SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column} =
                                a.${field.db.databaseRelation.joinFrom.joinColumn}
                                <choose>
                                    <when test="field.db.jdbcType.equals('datetime')">
                                        AND (b.${field.db.column} >=
                                        '${param.value.beginTime}'
                                        AND b.${field.db.column} &lt; '${param.value.endTime}')
                                    </when>
                                    <when test="field.db.jdbcType.equals('stringTime')">
                                        AND (
                                        cast(b.${field.db.column} as DATETIME) >=
                                        '${param.value.beginTime}'
                                        AND cast(b.${field.db.column} as DATETIME) &lt; '${param.value.endTime}'
                                        )
                                    </when>
                                    <when test="field.db.jdbcType.equals('dept_code')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="OR" close=")">
                                            b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <when test="field.db.jdbcType.equals('district')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="OR" close=")">
                                            b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <!-- jwzh码表，需要用前缀匹配 -->
                                    <when test="field.db.jdbcType.equals('jwzh')">
                                        AND b.${field.db.column} like concat(
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}',
                                        '%')
                                    </when>
                                    <when test="field.db.jdbcType.equals('array')">
                                        AND b.${field.db.column} in
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                            '${item}'
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND b.${field.db.column} = '${param.value}'
                                    </otherwise>
                                </choose>
                                )
                            </if>
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATED_TWO_TABLE">
                                AND EXISTS (
                                select c.${field.db.column}
                                from ${field.db.table} c, ${field.db.databaseRelation.table} b where
                                b.${field.db.databaseRelation.joinFrom.joinColumn} = c.${field.db.databaseRelation.column} and
                                b.${field.db.databaseRelation.joinTo.joinColumn}=a.id
                                <choose>
                                    <when test="field.db.jdbcType.equals('array')">
                                        AND c.${field.db.column} in
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="," close=")">
                                            '${item}'
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND c.${field.db.column} = '${param.value}'
                                    </otherwise>
                                </choose>
                                )
                            </if>
                        </otherwise>
                    </choose>
                </if>
            </foreach>

            <!-- 从module-databaseRelation和relatedId生成关联表检索条件 -->
            <if test="databaseRelation != null">
                <choose>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@JSON_ID_ARRAY">
                        AND a.id MEMBER OF ((SELECT j.${databaseRelation.column } FROM ${databaseRelation.table } j
                        WHERE j.id = ${relatedId}))
                    </when>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                        AND a.${databaseRelation.joinTo.column} IN (
                        SELECT t.${databaseRelation.joinTo.column} FROM ${databaseRelation.table} r
                        JOIN ${databaseRelation.joinFrom.table} f ON f.${databaseRelation.joinFrom.column} =
                        r.${databaseRelation.joinFrom.joinColumn}
                        JOIN ${databaseRelation.joinTo.table} t ON t.${databaseRelation.joinTo.column} =
                        r.${databaseRelation.joinTo.joinColumn}
                        <if test="databaseRelation.joinFrom.primaryKey != null">
                            WHERE f.${databaseRelation.joinFrom.primaryKey}=${relatedId}
                        </if>
                        <if test="databaseRelation.joinFrom.primaryKey == null">
                            WHERE f.${databaseRelation.joinFrom.column}=${relatedId}
                        </if>
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and r.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                        )
                    </when>
                    <when test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                        a.${databaseRelation.column} = ${relatedId}
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and a.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </if>
            <!-- 从dataPermission生成数据权限筛选条件 -->
            <if test="dataPermission != null and permissionInfo != null">
                <foreach collection="dataPermission" item="config">
                    <if test="config.level == permissionInfo.permission">
                        <choose>
                            <!-- 数据权限为本人/本部门/本地区 -->
                            <when
                                    test="permissionInfo.permission == 'SELF' or permissionInfo.permission == 'DEPT' or permissionInfo.permission == 'DISTRICT'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} = ${permissionInfo.getField(config.infoField)}
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} = '${permissionInfo.getField(config.infoField)}')
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为本部门及以下/本地区及以下 -->
                            <when
                                    test="permissionInfo.permission == 'DISTRICT_AND_CHILD' or permissionInfo.permission == 'DEPT_AND_CHILD'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                        )
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为ALL，不做处理 -->
                        </choose>
                    </if>
                </foreach>
            </if>

            <!--档案数据权限-->
            <if test="profileDataPermission != null and permissionInfo != null and permissionInfo.profilePermission != null">
                <foreach collection="profileDataPermission" item="profileDataPermission">
                    <choose>
                        <when
                                test="profileDataPermission.type == 'personLabel' and permissionInfo.profilePermission.personLabelIds != null ">
                            and
                            json_overlaps(a.${profileDataPermission.db.column},
                            #{permissionInfo.profilePermission.personLabelIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
                        </when>
                        <when
                                test="profileDataPermission.type == 'groupLabel' and permissionInfo.profilePermission.groupLabelIds != null ">
                            and
                            json_overlaps(a.${profileDataPermission.db.column},
                            #{permissionInfo.profilePermission.groupLabelIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
    </update>

    <select id="doSelectIds" resultType="java.lang.Long">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        <bind name="dataPermission" value="schema.dataPermission"/>
        <bind name="profileDataPermission" value="schema.profileDataPermission"/>
        select a.id as id
        from ${table} as a
        <where>
            <!-- 从filterParams生成筛选条件 -->
            <if test="filterParams!=null">
                <foreach collection="filterParams" item="param">
                    <bind name="field" value="schema.getField(param.key)"/>
                    <if test="field != null">
                        <choose>
                            <!-- 如果没有join则只查询本字段即可-->
                            <when test="field.db.databaseRelation == null">
                                <choose>
                                    <!-- 如果是标签则需要用path去like -->
                                    <when test="field.db.jdbcType.equals('label_id_array')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            JSON_OVERLAPS( a.${field.db.column},
                                            (
                                            SELECT JSON_ARRAYAGG( l.id )
                                            FROM t_profile_label l
                                            WHERE CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                                            ))> 0
                                        </foreach>
                                    </when>
                                    <!-- 时间范围,查全部时不加条件 -->
                                    <when test="param.type.equals('timeParams')">
                                        <if test="param.getProcessedValue().isAll() == false">
                                            AND (a.${field.db.column} >= '${param.value.beginTime}'
                                            AND a.${field.db.column} &lt;= '${param.value.endTime}')
                                        </if>
                                    </when>
                                    <!-- 如果是district需要去like -->
                                    <when test="field.db.jdbcType.equals('district')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            a.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <!-- 如果是部门id直接 in-->
                                    <when test="field.db.jdbcType.equals('dept_id')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            a.${field.db.column} in (SELECT d.id FROM t_dept d WHERE CONCAT( d.path, d.id,
                                            '-') LIKE CONCAT('%-', ${item}, '-%' ))
                                        </foreach>
                                    </when>
                                    <!-- 查询部门代码列表 -->
                                    <when test="field.db.jdbcType.equals('dept_code')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator=","
                                                 close=")">
                                            a.${field.db.column} in (SELECT d.code FROM t_dept d WHERE d.code like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%'))
                                        </foreach>
                                    </when>
                                    <when test="field.db.jdbcType.equals('case_label_array')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                                 close=")">
                                            JSON_OVERLAPS( a.${field.db.column},
                                            (
                                            SELECT JSON_ARRAYAGG( l.CODE )
                                            FROM t_profile_case_label l WHERE CONCAT( l.path, l.code, '-' )
                                            LIKE CONCAT('%-',#{item},'-%' )
                                            ))> 0
                                        </foreach>
                                    </when>
                                    <!-- 如果是jwzh码表需要去like -->
                                    <when test="field.db.jdbcType.equals('jwzh')">
                                        AND a.${field.db.column} like concat(
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}',
                                        '%')
                                    </when>
                                    <when test="field.db.jdbcType.equals('array')">
                                        AND a.${field.db.column} in
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                            '${item}'
                                        </foreach>
                                    </when>
                                    <!-- 直接本字段比较 -->
                                    <otherwise>
                                        AND a.${field.db.column} = '${param.value}'
                                    </otherwise>
                                </choose>
                            </when>
                            <when test="field.db.column=='count(1)'">
                                <if test="param.value==1">
                                    and (select count(0) from ${field.db.table} b where
                                    b.${field.db.databaseRelation.column} = a.id) >0
                                </if>
                                <if test="param.value==0">
                                    and (select count(0) from ${field.db.table} b where
                                    b.${field.db.databaseRelation.column} = a.id) =0
                                </if>
                            </when>
                            <otherwise>
                                <!-- 如果是外键关联需要用子查询 -->
                                <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                                    AND EXISTS (
                                    SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                    b.${field.db.databaseRelation.column } = a.id
                                    <choose>
                                        <!-- 如果是部门编号需要用prefix去like -->
                                        <when test="field.db.jdbcType.equals('dept_code')">
                                            AND
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="OR" close=")">
                                                b.${field.db.column} like concat(
                                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                                , '%')
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            AND b.${field.db.column}='${param.value}'
                                        </otherwise>
                                    </choose>
                                    )
                                </if>
                                <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                    AND EXISTS (
                                    SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                    b.${field.db.databaseRelation.column} =
                                    a.${field.db.databaseRelation.joinFrom.joinColumn}
                                    <choose>
                                        <when test="field.db.jdbcType.equals('datetime')">
                                            AND (b.${field.db.column} >=
                                            '${param.value.beginTime}'
                                            AND b.${field.db.column} &lt; '${param.value.endTime}')
                                        </when>
                                        <when test="field.db.jdbcType.equals('stringTime')">
                                            AND (
                                            cast(b.${field.db.column} as DATETIME) >=
                                            '${param.value.beginTime}'
                                            AND cast(b.${field.db.column} as DATETIME) &lt; '${param.value.endTime}'
                                            )
                                        </when>
                                        <when test="field.db.jdbcType.equals('dept_code')">
                                            AND
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="OR" close=")">
                                                b.${field.db.column} like concat(
                                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                                , '%')
                                            </foreach>
                                        </when>
                                        <when test="field.db.jdbcType.equals('district')">
                                            AND
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="OR" close=")">
                                                b.${field.db.column} like concat(
                                                '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                                , '%')
                                            </foreach>
                                        </when>
                                        <!-- jwzh码表，需要用前缀匹配 -->
                                        <when test="field.db.jdbcType.equals('jwzh')">
                                            AND b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}',
                                            '%')
                                        </when>
                                        <when test="field.db.jdbcType.equals('array')">
                                            AND b.${field.db.column} in
                                            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                                '${item}'
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            AND b.${field.db.column} = '${param.value}'
                                        </otherwise>
                                    </choose>
                                    )
                                </if>
                                <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATED_TWO_TABLE">
                                    AND EXISTS (
                                    select c.${field.db.column}
                                    from ${field.db.table} c, ${field.db.databaseRelation.table} b where
                                    b.${field.db.databaseRelation.joinFrom.joinColumn} = c.${field.db.databaseRelation.column} and
                                    b.${field.db.databaseRelation.joinTo.joinColumn}=a.id
                                    <choose>
                                        <when test="field.db.jdbcType.equals('array')">
                                            AND c.${field.db.column} in
                                            <foreach collection="param.getProcessedValue()" item="item" open="("
                                                     separator="," close=")">
                                                '${item}'
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            AND c.${field.db.column} = '${param.value}'
                                        </otherwise>
                                    </choose>
                                    )
                                </if>
                            </otherwise>
                        </choose>
                    </if>
                </foreach>
            </if>

            <!-- 从searchParams生成搜索条件 -->
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.getSearchValue())">
                <bind name="field" value="schema.getField(searchParams.searchField)"/>
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test="field != null">
                        <if test="field.db.databaseRelation != null">
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND a.${field.db.databaseRelation.column} in (
                                SELECT b.${field.db.databaseRelation.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column } =
                                a.${field.db.databaseRelation.joinFrom.joinColumn} and b.${field.db.column} like
                                #{pattern,jdbcType=VARCHAR}
                                )
                            </if>
                            <if test="field.db.databaseRelation.type != @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND a.${field.db.column} like #{pattern,jdbcType=VARCHAR}
                            </if>
                        </if>
                        <if test="field.db.databaseRelation == null">
                            AND a.${field.db.column} like #{pattern,jdbcType=VARCHAR}
                        </if>
                    </when>
                    <otherwise>
                        AND
                        <foreach collection="schema.searchFields" open="(" separator="OR" close=")" item="sf">
                            <bind name="f" value="schema.getField(sf.key)"/>
                            <if test="f.db.databaseRelation != null">
                                <choose>
                                    <when
                                            test="f.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                        a.${f.db.databaseRelation.column} in (
                                        SELECT b.${f.db.databaseRelation.column} from ${f.db.table} b WHERE
                                        b.${f.db.databaseRelation.column } =
                                        a.${f.db.databaseRelation.joinFrom.joinColumn} and b.${f.db.column} like
                                        #{pattern,jdbcType=VARCHAR}
                                        )
                                    </when>
                                    <otherwise>
                                        a.${f.db.column} like #{pattern,jdbcType=VARCHAR}
                                    </otherwise>
                                </choose>
                            </if>
                            <if test="f.db.databaseRelation == null">
                                a.${f.db.column} like #{pattern,jdbcType=VARCHAR}
                            </if>

                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <!-- 从module-databaseRelation和relatedId生成关联表检索条件 -->
            <if test="databaseRelation != null">
                <choose>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@JSON_ID_ARRAY">
                        AND a.id MEMBER OF ((SELECT j.${databaseRelation.column } FROM ${databaseRelation.table } j
                        WHERE j.id = ${relatedId}))
                    </when>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                        AND a.${databaseRelation.joinTo.column} IN (
                        SELECT t.${databaseRelation.joinTo.column} FROM ${databaseRelation.table} r
                        JOIN ${databaseRelation.joinFrom.table} f ON f.${databaseRelation.joinFrom.column} =
                        r.${databaseRelation.joinFrom.joinColumn}
                        JOIN ${databaseRelation.joinTo.table} t ON t.${databaseRelation.joinTo.column} =
                        r.${databaseRelation.joinTo.joinColumn}
                        <if test="databaseRelation.joinFrom.primaryKey != null">
                            WHERE f.${databaseRelation.joinFrom.primaryKey}=${relatedId}
                        </if>
                        <if test="databaseRelation.joinFrom.primaryKey == null">
                            WHERE f.${databaseRelation.joinFrom.column}=${relatedId}
                        </if>
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and r.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                        )
                    </when>
                    <when test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                        a.${databaseRelation.column} = ${relatedId}
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and a.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </if>

            <!-- 从dataPermission生成数据权限筛选条件 -->
            <if test="dataPermission != null and permissionInfo != null">
                <foreach collection="dataPermission" item="config">
                    <if test="config.level == permissionInfo.permission">
                        <choose>
                            <!-- 数据权限为本人/本部门/本地区 -->
                            <when
                                    test="permissionInfo.permission == 'SELF' or permissionInfo.permission == 'DEPT' or permissionInfo.permission == 'DISTRICT'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} = ${permissionInfo.getField(config.infoField)}
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} = '${permissionInfo.getField(config.infoField)}')
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为本部门及以下/本地区及以下 -->
                            <when
                                    test="permissionInfo.permission == 'DISTRICT_AND_CHILD' or permissionInfo.permission == 'DEPT_AND_CHILD'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                        )
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为ALL，不做处理 -->
                        </choose>
                    </if>
                </foreach>
            </if>

            <!--档案数据权限-->
            <if test="profileDataPermission != null and permissionInfo != null and permissionInfo.profilePermission != null">
                <foreach collection="profileDataPermission" item="profileDataPermission">
                    <choose>
                        <when
                                test="profileDataPermission.type == 'personLabel' and permissionInfo.profilePermission.personLabelIds != null ">
                            and
                            json_overlaps(a.${profileDataPermission.db.column},
                            #{permissionInfo.profilePermission.personLabelIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
                        </when>
                        <when
                                test="profileDataPermission.type == 'groupLabel' and permissionInfo.profilePermission.groupLabelIds != null ">
                            and
                            json_overlaps(a.${profileDataPermission.db.column},
                            #{permissionInfo.profilePermission.groupLabelIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
                        </when>
                    </choose>
                </foreach>
            </if>
            and a.deleted = 0
        </where>
    </select>

    <select id="getPageCount" resultType="java.lang.Long">
        <bind name="fields" value="schema.fields"/>
        <bind name="table" value="schema.table"/>
        <bind name="dataPermission" value="schema.dataPermission"/>
        <bind name="profileDataPermission" value="schema.profileDataPermission"/>
        select count(distinct a.id)
        from ${table} as a
        <if test="table = @com.trs.police.profile.schema.util.DbHelper@T_PROFILE_EVENT">
            left join t_profile_group_event_relation r on a.id = r.event_id
            LEFT JOIN t_profile_group g ON r.group_id = g.id
        </if>
        <where>
            <!-- 从filterParams生成筛选条件 -->
            <foreach collection="filterParams" item="param">
                <bind name="field" value="schema.getField(param.key)"/>
                <if test="param.key == 'policeKind'">
                    <if test="param.value == 4 or param.value == 3 or param.value == 99">
                        and a.police_kind is not null
                    </if>
                </if>
                <if test="param.key == 'ids'">
                    <if test="param.value != null and param.value.size > 0">
                        and a.id in
                        <foreach collection="param.value" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="field != null">
                    <choose>
                        <!-- 如果没有join则只查询本字段即可-->
                        <when test="field.db.databaseRelation == null">
                            <choose>
                                <!-- 如果是标签则需要用path去like -->
                                <when test="field.db.jdbcType.equals('label_id_array')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        JSON_OVERLAPS( a.${field.db.column},
                                        (
                                        SELECT JSON_ARRAYAGG( l.id )
                                        FROM t_profile_label l
                                        WHERE CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                                        ))> 0
                                    </foreach>
                                </when>
                                <!-- 时间范围,查全部时不加条件 -->
                                <when test="param.type.equals('timeParams')">
                                    <if test="param.getProcessedValue().isAll() == false">
                                        AND (a.${field.db.column} >= '${param.value.beginTime}'
                                        AND a.${field.db.column} &lt;= '${param.value.endTime}')
                                    </if>
                                </when>
                                <!-- 如果是district需要去like -->
                                <when test="field.db.jdbcType.equals('district')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        a.${field.db.column} like concat(
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                        , '%')
                                    </foreach>
                                </when>
                                <!-- 如果是部门id直接 in-->
                                <when test="field.db.jdbcType.equals('dept_id')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        a.${field.db.column} in (SELECT d.id FROM t_dept d WHERE CONCAT( d.path, d.id,
                                        '-') LIKE CONCAT('%-', ${item}, '-%' ))
                                    </foreach>
                                </when>
                                <!-- 查询部门代码列表 -->
                                <when test="field.db.jdbcType.equals('dept_code')">
                                    AND a.${field.db.column} in (SELECT d.code FROM t_dept d WHERE
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator=" OR " close=")">
                                        d.code like concat(
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}',
                                        '%')
                                    </foreach>
                                    )
                                </when>
                                <when test="field.db.jdbcType.equals('case_label_array')">
                                    AND
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                             close=")">
                                        JSON_OVERLAPS( a.${field.db.column},
                                        (
                                        SELECT JSON_ARRAYAGG( l.CODE )
                                        FROM t_profile_case_label l WHERE CONCAT( l.path, l.code, '-' )
                                        LIKE CONCAT('%-',#{item},'-%' )
                                        ))> 0
                                    </foreach>
                                </when>
                                <!-- 如果是jwzh码表需要去like -->
                                <when test="field.db.jdbcType.equals('jwzh')">
                                    AND left(a.${field.db.column},
                                    ${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value).length()})
                                    =
                                    '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}'
                                </when>
                                <when test="field.db.jdbcType.equals('array')">
                                    AND a.${field.db.column} in
                                    <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                        '${item}'
                                    </foreach>
                                </when>
                                <!-- 直接本字段比较 -->
                                <otherwise>
                                    AND a.${field.db.column} = '${param.value}'
                                </otherwise>
                            </choose>
                        </when>
                        <when test="field.db.column=='count(1)'">
                            <if test="param.value==1">
                                and (select count(0) from ${field.db.table} b where
                                b.${field.db.databaseRelation.column} = a.id) >0
                            </if>
                            <if test="param.value==0">
                                and (select count(0) from ${field.db.table} b where
                                b.${field.db.databaseRelation.column} = a.id) =0
                            </if>
                        </when>
                        <when test="field.db.jdbcType == 'eventRelatedGroup'">
                            and
                            (
                            JSON_OVERLAPS (
                            g.group_label,
                            (
                            SELECT
                            JSON_ARRAYAGG( l.id )
                            FROM
                            t_profile_label l
                            WHERE
                            <foreach collection="param.getProcessedValue()" item="item" open="(" separator="OR"
                                     close=")">
                                CONCAT( l.path, l.id, '-') LIKE CONCAT('%-', ${item}, '-%' )
                            </foreach>
                            ))> 0
                            )
                        </when>
                        <otherwise>
                            <!-- 如果是外键关联需要用子查询 -->
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                                AND EXISTS (
                                SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column } = a.id
                                <choose>
                                    <!-- 如果是部门编号需要用prefix去like -->
                                    <when test="field.db.jdbcType.equals('dept_code')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="OR" close=")">
                                            b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND b.${field.db.column}='${param.value}'
                                    </otherwise>
                                </choose>
                                )
                            </if>
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND EXISTS (
                                SELECT b.${field.db.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column} =
                                a.${field.db.databaseRelation.joinFrom.joinColumn}
                                <choose>
                                    <when test="field.db.jdbcType.equals('datetime')">
                                        AND (b.${field.db.column} >=
                                        '${param.value.beginTime}'
                                        AND b.${field.db.column} &lt; '${param.value.endTime}')
                                    </when>
                                    <when test="field.db.jdbcType.equals('stringTime')">
                                        AND (
                                        cast(b.${field.db.column} as DATETIME) >=
                                        '${param.value.beginTime}'
                                        AND cast(b.${field.db.column} as DATETIME) &lt; '${param.value.endTime}'
                                        )
                                    </when>
                                    <when test="field.db.jdbcType.equals('dept_code')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="OR" close=")">
                                            b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <when test="field.db.jdbcType.equals('district')">
                                        AND
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="OR" close=")">
                                            b.${field.db.column} like concat(
                                            '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(item)}'
                                            , '%')
                                        </foreach>
                                    </when>
                                    <!-- jwzh码表，需要用前缀匹配 -->
                                    <when test="field.db.jdbcType.equals('jwzh')">
                                        AND left(b.${field.db.column},
                                        ${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value).length()})
                                        =
                                        '${@com.trs.police.common.core.utils.StringUtil@getPrefixCode(param.value)}'
                                    </when>
                                    <when test="field.db.jdbcType.equals('array')">
                                        AND b.${field.db.column} in
                                        <foreach collection="param.getProcessedValue()" item="item" open="(" separator="," close=")">
                                            '${item}'
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND b.${field.db.column} = '${param.value}'
                                    </otherwise>
                                </choose>
                                )
                            </if>
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATED_TWO_TABLE">
                                AND EXISTS (
                                select c.${field.db.column}
                                from ${field.db.table} c, ${field.db.databaseRelation.table} b where
                                b.${field.db.databaseRelation.joinFrom.joinColumn} = c.${field.db.databaseRelation.column} and
                                b.${field.db.databaseRelation.joinTo.joinColumn}=a.id
                                <choose>
                                    <when test="field.db.jdbcType.equals('array')">
                                        AND c.${field.db.column} in
                                        <foreach collection="param.getProcessedValue()" item="item" open="("
                                                 separator="," close=")">
                                            '${item}'
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        AND c.${field.db.column} = '${param.value}'
                                    </otherwise>
                                </choose>
                                )
                            </if>
                        </otherwise>
                    </choose>
                </if>
                <!-- 人员、事件、群体管控警种筛选特殊逻辑 -->
                <if test="field == null and param.value != null and param.value != '' and  'controlPoliceKind' == param.key and relatedId == null
                and (table == 't_profile_group' or table == 't_profile_person' or table == 't_profile_event')">
                <foreach collection='param.value.toString().split(",")' separator="or" item="item" open="AND (" close=")">
                    JSON_CONTAINS(a.police_kind, #{item})
                    <!-- 加上审批权限 -->
                        and JSON_CONTAINS(a.police_kind, '${item}')
                    and (
                        a.approval_detail is null
                    or (
                        JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${item}".status'), '3')
                        or JSON_CONTAINS(JSON_EXTRACT(a.approval_detail, '$."${item}".approvalUserId'), '${permissionInfo.userId}')
                        or a.create_dept_id = #{permissionInfo.deptId}
                        )
                    )
                </foreach>
                </if>
            </foreach>

            <!-- 从searchParams生成搜索条件 -->
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(searchParams.getSearchValue())">
                <bind name="field" value="schema.getField(searchParams.searchField)"/>
                <bind name="pattern" value="'%' + searchParams.getSearchValue() + '%'"/>
                <choose>
                    <when test="field != null">
                        <if test="field.db.databaseRelation != null">
                            <if test="field.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND a.${field.db.databaseRelation.column} in (
                                SELECT b.${field.db.databaseRelation.column} from ${field.db.table} b WHERE
                                b.${field.db.databaseRelation.column } =
                                a.${field.db.databaseRelation.joinFrom.joinColumn} and b.${field.db.column} like
                                #{pattern,jdbcType=VARCHAR}
                                )
                            </if>
                            <if test="field.db.databaseRelation.type != @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                AND a.${field.db.column} like #{pattern,jdbcType=VARCHAR}
                            </if>
                        </if>
                        <if test="field.db.databaseRelation == null">
                            AND a.${field.db.column} like #{pattern,jdbcType=VARCHAR}
                        </if>
                    </when>
                    <otherwise>
                        AND
                        <foreach collection="schema.searchFields" open="(" separator="OR" close=")" item="sf">
                            <bind name="f" value="schema.getField(sf.key)"/>
                            <if test="f.db.databaseRelation != null">
                                <choose>
                                    <when
                                            test="f.db.databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@IMPORT_TABLE">
                                        a.${f.db.databaseRelation.column} in (
                                        SELECT b.${f.db.databaseRelation.column} from ${f.db.table} b WHERE
                                        b.${f.db.databaseRelation.column } =
                                        a.${f.db.databaseRelation.joinFrom.joinColumn} and b.${f.db.column} like
                                        #{pattern,jdbcType=VARCHAR}
                                        )
                                    </when>
                                    <otherwise>
                                        a.${f.db.column} like #{pattern,jdbcType=VARCHAR}
                                    </otherwise>
                                </choose>
                            </if>
                            <if test="f.db.databaseRelation == null">
                                a.${f.db.column} like #{pattern,jdbcType=VARCHAR}
                            </if>

                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <!-- 从module-databaseRelation和relatedId生成关联表检索条件 -->
            <if test="databaseRelation != null">
                <choose>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@JSON_ID_ARRAY">
                        AND a.id MEMBER OF ((SELECT j.${databaseRelation.column } FROM ${databaseRelation.table } j
                        WHERE j.id = ${relatedId}))
                    </when>
                    <when
                            test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@RELATION_TABLE">
                        AND a.${databaseRelation.joinTo.column} IN (
                        SELECT t.${databaseRelation.joinTo.column} FROM ${databaseRelation.table} r
                        JOIN ${databaseRelation.joinFrom.table} f ON f.${databaseRelation.joinFrom.column} =
                        r.${databaseRelation.joinFrom.joinColumn}
                        JOIN ${databaseRelation.joinTo.table} t ON t.${databaseRelation.joinTo.column} =
                        r.${databaseRelation.joinTo.joinColumn}
                        <if test="databaseRelation.joinFrom.primaryKey != null">
                            WHERE f.${databaseRelation.joinFrom.primaryKey}=${relatedId}
                        </if>
                        <if test="databaseRelation.joinFrom.primaryKey == null">
                            WHERE f.${databaseRelation.joinFrom.column}=${relatedId}
                        </if>
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and r.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                        )
                    </when>
                    <when test="databaseRelation.type == @com.trs.police.profile.schema.db.DatabaseRelationType@FOREIGN_KEY">
                        a.${databaseRelation.column} = ${relatedId}
                        <if test="databaseRelation!=null and databaseRelation.extendCondition!=null and databaseRelation.extendCondition.length>0">
                            <foreach collection="databaseRelation.extendCondition" item="condition">
                                and a.${condition.column} = ${condition.value}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </if>

            <!-- 从dataPermission生成数据权限筛选条件 -->
            <if test="dataPermission != null and permissionInfo != null">
                <foreach collection="dataPermission" item="config">
                    <if test="config.level == permissionInfo.permission">
                        <choose>
                            <!-- 数据权限为本人/本部门/本地区 -->
                            <when
                                    test="permissionInfo.permission == 'SELF' or permissionInfo.permission == 'DEPT' or permissionInfo.permission == 'DISTRICT'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} = ${permissionInfo.getField(config.infoField)}
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} = '${permissionInfo.getField(config.infoField)}')
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为本部门及以下/本地区及以下 -->
                            <when
                                    test="permissionInfo.permission == 'DISTRICT_AND_CHILD' or permissionInfo.permission == 'DEPT_AND_CHILD'">
                                <choose>
                                    <when test="config.db.databaseRelation == null">
                                        and a.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                    </when>
                                    <otherwise>
                                        and exists (select * from ${config.db.table} c where
                                        a.${config.db.databaseRelation.joinFrom.joinColumn} =
                                        c.${config.db.databaseRelation.column}
                                        and c.${config.db.column} in
                                        <foreach collection="permissionInfo.getField(config.infoField)" item="code"
                                                 open="(" separator="," close=")">
                                            #{code}
                                        </foreach>
                                        )
                                    </otherwise>
                                </choose>

                            </when>
                            <!-- 数据权限为ALL，不做处理 -->
                        </choose>
                    </if>
                </foreach>
            </if>

            <!-- 档案数据权限 （根据档案类型） -->
            <include refid="profilePermissionCondition"/>
            and a.deleted = 0
        </where>

    </select>

</mapper>