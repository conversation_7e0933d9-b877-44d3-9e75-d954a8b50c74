package com.trs.police.incident.analysis.domain.request;

import com.trs.police.common.core.params.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * 获取学校库列表请求参数
 */

@Data
@ApiModel("获取学校库列表请求参数")
public class GetSchoolLibraryListRequest {

    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private PageParams pageParams;

    /**
     * 检索字段 schoolName:学校名称 schoolAlias:学校别名
     */
    @ApiModelProperty("检索字段 school_name:学校名称 school_aliases:学校别名")
    @Pattern(regexp = "^(school_name|school_aliases)$", message = "检索字段非法")
    private String searchColumn;

    /**
     * 检索值
     */
    @ApiModelProperty("检索值")
    private String searchValue;

}
