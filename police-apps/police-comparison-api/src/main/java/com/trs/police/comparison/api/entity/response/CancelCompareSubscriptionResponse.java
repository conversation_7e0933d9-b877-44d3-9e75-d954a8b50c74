package com.trs.police.comparison.api.entity.response;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 取消比对订阅服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/15 16:40
 */
@Data
@NoArgsConstructor
public class CancelCompareSubscriptionResponse extends NonIdempotentStandardResponse implements DataServiceAPIBaseResponse {

  public static CancelCompareSubscriptionResponse success() {
    CancelCompareSubscriptionResponse response = new CancelCompareSubscriptionResponse();
    response.setCode(200);
    return response;
  }

}
