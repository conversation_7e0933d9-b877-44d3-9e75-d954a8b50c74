<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.police.subject.common.mapper.WarningFkrxyjMapper">

    <select id="warningStatistics" resultType="com.trs.police.subject.domain.vo.PersonalStatisticsVO">
        SELECT
            count(1) AS warningCount,
            `group` as areaName
        FROM t_warning_fkrxyj
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND create_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY `group`
    </select>

    <select id="profilePersonFkgzStatistic" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM t_profile_person tpp
        <where>
            tpp.deleted = 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND tpp.create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND tpp.create_time &lt;= #{dto.endTime}
            </if>
            <if test="fkLabelIds != null and fkLabelIds != ''">
                AND JSON_OVERLAPS(tpp.person_label , #{fkLabelIds})
            </if>
        </where>
    </select>

    <select id="getProfilePersonFkgzCount" resultType="com.trs.police.common.core.vo.control.AreaListVO">
        SELECT
            pc.control_station AS districtCode,
            count(1) AS count
        FROM t_profile_person tpp
        LEFT JOIN t_profile_person_police_control pc ON tpp.id = pc.person_id
        <where>
            tpp.deleted = 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND tpp.create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND tpp.create_time &lt;= #{dto.endTime}
            </if>
            <if test="fkLabelIds != null and fkLabelIds != ''">
                AND JSON_OVERLAPS(tpp.person_label , #{fkLabelIds})
            </if>
        </where>
        GROUP BY pc.control_station
    </select>

    <select id="warningPersonStatistic" resultType="java.lang.Long">
        select
        count(distinct id_card)
        from t_warning_fkrxyj
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND create_time &lt;= #{dto.endTime}
            </if>
            <if test="warningModel != null and warningModel != ''">
                AND warning_model = #{warningModel}
            </if>
            <if test="warningLevel != null and warningLevel.size() > 0">
                AND warning_level in
                <foreach collection="warningLevel" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getWarningPersonCount" resultType="com.trs.police.common.core.vo.control.AreaListVO">
        select
            `group` AS districtName,
            count(distinct id_card) AS count
        from t_warning_fkrxyj
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND create_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND create_time &lt;= #{dto.endTime}
            </if>
            <if test="warningModel != null and warningModel != ''">
                AND warning_model = #{warningModel}
            </if>
            <if test="warningLevel != null and warningLevel.size() > 0">
                AND warning_level in
                <foreach collection="warningLevel" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by `group`
    </select>

    <select id="firstInfoPersonStatistic" resultType="java.lang.Long">
        select
            count(distinct id_card)
        from t_warning_fk_person
        <where>
            person_status = 0
            <if test="dto.startTime != null and dto.startTime != ''">
                AND first_into_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND first_into_time &lt;= #{dto.endTime}
            </if>
        </where>
    </select>


    <select id="selectFkWarningList" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select
        <choose>
            <when test="isPcs == true">
                d.code as `key`,
            </when>
            <otherwise>
                d.district_code as `key`,
            </otherwise>
        </choose>
        count(distinct yj.id) as count
        from t_warning_fkrxyj yj
        <if test="isJudged == true">
            left join t_warning_fk_person_ryrp r on yj.id = r.warning_id
        </if>
        left join t_warning_fk_person p on yj.id_card = p.id_card
        <if test="dto.areaCode != null and dto.areaCode != ''">
            join
            JSON_TABLE(p.zrpcs, '$[*]' COLUMNS ( dept_id INT PATH '$' )) AS jt
            JOIN
            t_dept d ON jt.dept_id = d.id
        </if>
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND yj.capture_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND yj.capture_time &lt;= #{dto.endTime}
            </if>
            <if test="isJudged == true">
                and r.warning_id is not null
            </if>
            <if test="level != null and level != ''">
                AND yj.warning_level = #{level}
            </if>
            <if test="isJudged">
                AND p.person_status = 1
            </if>
            <if test="dto.areaCode != null and dto.areaCode != ''">
                and d.code like concat('',#{dto.areaCode},'%')
            </if>
        </where>
        group by
        <choose>
            <when test="isPcs == true">
                d.code
            </when>
            <otherwise>
                d.district_code
            </otherwise>
        </choose>
    </select>

    <select id="countWarningByModel" resultType="com.trs.police.statistic.domain.bean.CountItem">
        select warning_model as `key`,count(1) as `count` from t_warning_fkrxyj
        <where>
            <if test="dto.startTime != null and dto.startTime != ''">
                AND capture_time &gt;= #{dto.startTime}
            </if>
            <if test="dto.endTime != null and dto.endTime != ''">
                AND capture_time &lt;= #{dto.endTime}
            </if>
            <if test="idCards != null and idCards.size() > 0">
                AND id_card in
                <foreach collection="idCards" item="idCard" open="(" close=")" separator=",">
                    #{idCard}
                </foreach>
            </if>
        </where>
        group by warning_model
    </select>

</mapper>