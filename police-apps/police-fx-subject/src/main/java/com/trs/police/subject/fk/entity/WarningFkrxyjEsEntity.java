package com.trs.police.subject.fk.entity;

import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.pojo.BaseRecordDO;
import com.trs.police.common.core.dto.WarningSource;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 反恐人像预警(高新)
 *
 * <AUTHOR>
 * @Date 2023/12/6 11:02
 */
@Data
@NoArgsConstructor
@TableName(value = "ys_control_gjxx")
public class WarningFkrxyjEsEntity extends BaseRecordDO {

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "alert_type")
    private String alertType;

    @TableField(value = "capture_address")
    private String captureAddress;

    @TableField(value = "capture_time")
    private Date captureTime;

    @TableField(value = "date")
    private Date date;

    @TableField(value = "device_name")
    private String deviceName;

    @TableField(value = "device_code")
    private String deviceCode;

    @TableField(value = "face_photo")
    private String facePhoto;

    @TableField(value = "id_card")
    private String idCard;

    @TableField(value = "latitude")
    private String latitude;

    @TableField(value = "lib_name")
    private String libName;

    @TableField(value = "longitude")
    private String longitude;

    @TableField(value = "photo")
    private String photo;

    @TableField(value = "similarity")
    private Double similarity;


    /**
     * 人员姓名
     */
    @TableField(value = "name")
    private String name;

    /**
     * 坐标
     */
    @TableField(value = "location", objectValue = true)
    private String location;

    /**
     * 感知源 {@link WarningSource}
     */
    @TableField(value = "warning_source", objectValue = true)
    private String warningSource;
}
