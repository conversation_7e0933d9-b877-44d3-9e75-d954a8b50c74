package com.trs.police.subject.sw.service.scene;

import com.trs.police.subject.domain.dto.ClueExcavateDTO;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.common.service.scene.ISubjectStatisticScene;

import java.util.List;

/**
 * sw专题-线索挖掘-基础抽象类
 *
 * @param <T> 搜索结果类型
 * <AUTHOR>
 * @date 2025/4/16
 */
public abstract class AbstractSwClueExcavateAnalysisImpl<T> implements ISubjectStatisticScene<T, ClueExcavateDTO> {

    /**
     * 总数
     */
    protected Long total = 0L;

    @Override
    public List<T> search(SubjectSceneContext<ClueExcavateDTO> context) {
        ClueExcavateDTO dto = context.getDto();
        if (dto == null) {
            dto = new ClueExcavateDTO();
            context.setDto(dto);
        }
        return doSearch(context);
    }

    /**
     * 获取总数
     *
     * @return 总数
     */
    public Long getTotal() {
        return total;
    }

    /**
     * 执行搜索
     *
     * @param context 上下文
     * @return 搜索结果
     */
    protected abstract List<T> doSearch(SubjectSceneContext<ClueExcavateDTO> context);
}
