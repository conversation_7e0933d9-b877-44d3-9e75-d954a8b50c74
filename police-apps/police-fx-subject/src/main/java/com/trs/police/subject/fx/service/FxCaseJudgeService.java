package com.trs.police.subject.fx.service;

import com.trs.police.common.core.vo.PageResult;
import com.trs.police.subject.domain.dto.PersonRelationAtlasDTO;
import com.trs.police.subject.domain.dto.SpecialCaseJudgeDTO;
import com.trs.police.subject.domain.vo.SpecialCaseJudgeVO;
import com.trs.web.builder.base.RestfulResultsV2;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * @author: tang.shuai
 * @date: 2024/04/22
 * @description: fx专题服务层
 */
public interface FxCaseJudgeService {

    /**
     * 专案研判列表
     *
     * @param judgeDTO judgeDTO
     * @return {@link PageResult}<{@link SpecialCaseJudgeVO}>
     */
    PageResult<SpecialCaseJudgeVO> specialCaseJudgeList(SpecialCaseJudgeDTO judgeDTO);

    /**
     * 新增专案研判
     *
     * @param judgeDTO judgeDTO
     */
    void addCaseJudge(SpecialCaseJudgeDTO judgeDTO);

    /**
     * 删除专案研判
     *
     * @param ids judgeDTO
     */
    void deleteCaseJudge(String ids);

    /**
     * 导出专案研判
     *
     * @param dto      dto
     * @param response response
     * @throws IOException IOException
     */
    void exportCaseJudge(SpecialCaseJudgeDTO dto, HttpServletResponse response);

    /**
     * 获取人员关系图谱
     *
     * @param dto dto
     * @return RestfulResultsV2
     */
    RestfulResultsV2 getPersonRelationAtlas(SpecialCaseJudgeDTO dto);

    /**
     * 编辑人员关系图谱
     *
     * @param dto dto
     */
    void addOrEditPersonRelationAtlas(PersonRelationAtlasDTO dto);

    /**
     * 删除人员关系图谱
     *
     * @param id  id
     * @param isDel isDel
     */
    void deletePersonRelationAtlas(String id,Integer isDel);
}
