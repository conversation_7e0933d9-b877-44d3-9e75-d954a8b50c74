package com.trs.police.subject.fx.controller;

import com.trs.police.subject.fx.service.otherClue.IOtherClueSync;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * @author: dingkeyu
 * @date: 2024/07/04
 * @description:
 */
@RestController
@RequestMapping("/public/otherClue")
public class FxOtherClueSyncController {

    @Resource
    private IOtherClueSync otherClueSync;

    /**
     * 其他线索挖掘同步接口
     *
     * @return {@link RestfulResultsV2}<{@link String}>
     */
    @GetMapping("/syncFxOtherClue")
    public RestfulResultsV2<String> syncFxHideout() {
        otherClueSync.syncFxOtherClue();
        return RestfulResultsV2.ok("success");
    }

}
