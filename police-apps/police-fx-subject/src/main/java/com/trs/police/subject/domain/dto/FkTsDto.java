package com.trs.police.subject.domain.dto;

import com.trs.web.builder.base.DTO.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 反恐态势dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FkTsDto extends BaseDTO {

    /**
     * 区域代码
     */
    private String areaCode;

    /**
     * 人员状态
     */
    private Integer personStatus;

    /**
     * 人员类别
     */
    private String personType;

    private List<String> keys;
}
