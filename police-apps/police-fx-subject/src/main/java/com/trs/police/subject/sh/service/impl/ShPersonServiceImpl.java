package com.trs.police.subject.sh.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.common.utils.expression.Operator;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.entity.ImportantAreaEntity;
import com.trs.police.common.core.entity.ThemeGjxxbEntity;
import com.trs.police.common.core.fx.entity.Person;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.common.core.utils.GeoHashUtils;
import com.trs.police.common.core.vo.AreaStatisticsVO;
import com.trs.police.common.core.vo.Dict2VO;
import com.trs.police.common.core.vo.control.AreaListVO;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.subject.common.mapper.ImportantAreaMapper;
import com.trs.police.subject.common.mapper.ShPersonMapper;
import com.trs.police.subject.common.mapper.WarningPersonClueMapper;
import com.trs.police.subject.common.repository.ThemeGjxxbRepository;
import com.trs.police.subject.domain.dto.PersonDTO;
import com.trs.police.subject.domain.dto.StatisticsDTO;
import com.trs.police.subject.domain.vo.PersonTrackVO;
import com.trs.police.subject.domain.vo.PersonTypeStatisticsVO;
import com.trs.police.subject.domain.vo.PersonVO;
import com.trs.police.subject.domain.vo.PersonalStatisticsVO;
import com.trs.police.subject.fx.converter.FxPersonConverter;
import com.trs.police.subject.helper.DistrictHelper;
import com.trs.police.subject.personclue.config.PersonClueCareMonitorConfig;
import com.trs.police.subject.sh.service.ShPersonService;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.entity.PageInfo;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * @author: dingkeyu
 * @date: 2024/11/08
 * @description:
 */
@Slf4j
@Service
public class ShPersonServiceImpl implements ShPersonService {

    @Autowired
    private ShPersonMapper shPersonMapper;

    @Autowired
    private DistrictHelper districtHelper;

    @Autowired
    private PersonClueCareMonitorConfig personClueCareMonitorConfig;

    @Autowired
    private ImportantAreaMapper importantAreaMapper;

    @Autowired
    private WarningPersonClueMapper warningPersonClueMapper;

    @Autowired
    private DictService dictService;

    @Autowired
    private ThemeGjxxbRepository themeGjxxbRepository;

    private Map<Long, String> areaTagMap = new HashMap<>();

    /**
     * init
     */
    @PostConstruct
    public void init() {
        List<Dict2VO> dict2Vos = dictService.commonSearch("important_area_tag", null, null, null);
        areaTagMap = dict2Vos.stream().collect(Collectors.toMap(Dict2VO::getCode, Dict2VO::getName));
    }


    @Override
    public RestfulResultsV2<PersonalStatisticsVO> arealDistribution(StatisticsDTO dto) {
        // 按照管控区域分组统计
        List<PersonalStatisticsVO> vos = shPersonMapper.arealDistribution(dto);
        List<PersonalStatisticsVO> otherVos = shPersonMapper.getOtherCount(dto);
        List<PersonalStatisticsVO> clueVos = shPersonMapper.getClueCount(dto);
        Map<String, PersonalStatisticsVO> map = vos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaCode, Function.identity()));
        Map<String, PersonalStatisticsVO> otherMap = otherVos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaCode, Function.identity()));
        Map<String, PersonalStatisticsVO> clueMap = clueVos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getAreaCode, Function.identity()));
        vos.forEach(vo -> {
            PersonalStatisticsVO otherVo = otherMap.get(vo.getAreaCode());
            if (Objects.nonNull(otherVo)) {
                vo.setWarningCount(otherVo.getWarningCount());
                vo.setActiveCount(otherVo.getActiveCount());
            }
            PersonalStatisticsVO clueVo = clueMap.get(vo.getAreaCode());
            if (Objects.nonNull(clueVo)) {
                vo.setClueCount(clueVo.getClueCount());
            }
        });
        // 获取高新区的11个派出所
        List<Map.Entry<String, String>> categoryList = districtHelper.categoryList(dto.getSubjectType());
        List<PersonalStatisticsVO> voList = new ArrayList<>();
        for (Map.Entry<String, String> entity : categoryList) {
            PersonalStatisticsVO statisticsVO = map.get(entity.getValue());
            if (Objects.nonNull(statisticsVO)) {
                statisticsVO.setAreaName(entity.getKey().replace("派出所", ""));
                statisticsVO.setAreaShowName(entity.getKey());
                voList.add(statisticsVO);
            } else {
                PersonalStatisticsVO vo = new PersonalStatisticsVO();
                vo.setAreaCode(entity.getValue());
                vo.setAreaName(entity.getKey().replace("派出所", ""));
                vo.setAreaShowName(entity.getKey());
                voList.add(vo);
            }
        }
        return RestfulResultsV2.ok(voList);
    }

    @Override
    public RestfulResultsV2<PersonTypeStatisticsVO> categoricalDistribution(StatisticsDTO dto) {
        // 人员类别分析不受时间筛选影响
        dto.setStartTime(null);
        dto.setEndTime(null);
        List<PersonalStatisticsVO> vos = shPersonMapper.categoricalDistribution(dto);
        Map<String, Long> map = vos.stream().collect(Collectors.toMap(PersonalStatisticsVO::getPersonTypeName, PersonalStatisticsVO::getTotalCount));
        List<String> defaultList = Arrays.asList("嫖客", "卖淫女", "组织者");
        List<PersonTypeStatisticsVO> results = defaultList.stream().map(type -> {
            PersonTypeStatisticsVO personTypeStatisticsVO = new PersonTypeStatisticsVO();
            personTypeStatisticsVO.setPersonTypeName(type);
            personTypeStatisticsVO.setTotalCount(map.getOrDefault(type, 0L));
            personTypeStatisticsVO.setChildren(null);
            return personTypeStatisticsVO;
        }).collect(Collectors.toList());
        return RestfulResultsV2.ok(results);
    }

    @Override
    public RestfulResultsV2<PersonalStatisticsVO> topStatistics(StatisticsDTO dto) {
        // 本期数据
        PersonalStatisticsVO currentVo = shPersonMapper.topStatistics(dto);
        Tuple2<String, String> ratioTimeTuple = getRatioTime(TimeUtils.stringToString(dto.getStartTime(), TimeUtils.YYYYMMDD),
                TimeUtils.stringToString(dto.getEndTime(), TimeUtils.YYYYMMDD));
        dto.setStartTime(ratioTimeTuple._1);
        dto.setEndTime(ratioTimeTuple._2);
        // 上期数据
        PersonalStatisticsVO ratioVo = shPersonMapper.topStatistics(dto);
        currentVo.setWarningRatio(computeRatio(currentVo.getWarningCount(), ratioVo.getWarningCount()));
        currentVo.setActiveRatio(computeRatio(currentVo.getActiveCount(), ratioVo.getActiveCount()));
        return RestfulResultsV2.ok(currentVo);
    }

    @Override
    public RestfulResultsV2<JSONObject> activeStatistic(StatisticsDTO dto) {
        List<AreaStatisticsVO> list = shPersonMapper.activeStatistic(dto);
        Map<String, Long> map = list.stream().collect(Collectors.toMap(AreaStatisticsVO::getTime, AreaStatisticsVO::getActiveCount));
        final List<String> dateList = TimeUtils.getDateList(TimeUtils.stringToString(dto.getStartTime(), YYYYMMDD),
                TimeUtils.stringToString(dto.getEndTime(), YYYYMMDD), YYYYMMDD);
        List<JSONObject> result = new ArrayList<>();
        dateList.forEach(date -> {
            Long count = map.getOrDefault(date, 0L);
            JSONObject obj = new JSONObject();
            obj.put("time", date);
            obj.put("activeCount", count);
            result.add(obj);
        });
        return RestfulResultsV2.ok(result);
    }

    @Override
    public RestfulResultsV2<AreaListVO> controlArea(StatisticsDTO dto, PageParams pageParams) {
        // 拿到配置的重点区域id
        List<Long> dwdMypcAreaId = personClueCareMonitorConfig.getDwdMypcAreaId();
        List<Long> dwdZzmypcAreaId = personClueCareMonitorConfig.getDwdZzmypcAreaId();
        List<Long> mergedList = Stream.concat(dwdMypcAreaId.stream(), dwdZzmypcAreaId.stream())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(mergedList)) {
            return RestfulResultsV2.ok(new ArrayList<>());
        }
        Page<ImportantAreaEntity> pageList = importantAreaMapper.selectPage(pageParams.toPage(), new QueryWrapper<ImportantAreaEntity>()
                .in("id", mergedList));
        List<AreaListVO> results = pageList.getRecords().stream().map(area -> {
            AreaListVO vo = new AreaListVO();
            vo.setId(area.getId());
            vo.setName(area.getName());
            vo.setDistrictCode(area.getDistrictCode());
            vo.setDistrictName(area.getDistrictName());
            vo.setTag(area.getTag());
            vo.setTagName(areaTagMap.get(vo.getTag()));
            return vo;
        }).collect(Collectors.toList());
        return RestfulResultsV2.ok(results)
                .addPageNum(pageParams.getPageNumber())
                .addPageSize(pageParams.getPageSize())
                .addTotalCount(pageList.getTotal());
    }

    @Override
    public RestfulResultsV2<JSONObject> clueExcavate(StatisticsDTO dto, PageParams pageParams) {
        RestfulResultsV2<AreaListVO> datas = controlArea(dto, pageParams);
        if (CollectionUtils.isEmpty(datas.getDatas())) {
            return RestfulResultsV2.ok(new ArrayList<>());
        }
        List<AreaListVO> list = warningPersonClueMapper.warningPersonClueById(dto);
        Map<Long, Integer> map = list.stream().collect(Collectors.toMap(AreaListVO::getId, AreaListVO::getWarningCount));

        JSONArray jsonArray = new JSONArray();
        datas.getDatas().forEach(area -> {
            JSONObject obj = new JSONObject();
            obj.put("modelName", area.getName());
            Integer warningCount = map.getOrDefault(area.getId(), 0);
            obj.put("warningCount", warningCount);
            jsonArray.add(obj);
        });
        // 计算总数
        int totalCount = jsonArray.stream()
                .map(obj -> ((JSONObject) obj).getInteger("warningCount"))
                .mapToInt(Integer::intValue)
                .sum();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("warningCount", totalCount);
        jsonObject.put("node", jsonArray);
        return RestfulResultsV2.ok(jsonObject)
                .addPageNum(pageParams.getPageNumber())
                .addPageSize(pageParams.getPageSize())
                .addTotalCount(datas.getSummary().getTotal());
    }

    @Override
    public RestfulResultsV2<PersonalStatisticsVO> warningStatistics(StatisticsDTO dto) {
        List<AreaListVO> areaListVos = warningPersonClueMapper.warningPersonClueByCode(dto, null);
        Map<String, AreaListVO> map = areaListVos.stream().collect(Collectors.toMap(AreaListVO::getDistrictCode, Function.identity()));
        // 获取高新区的11个派出所
        List<Map.Entry<String, String>> categoryList = districtHelper.categoryList(dto.getSubjectType());
        List<PersonalStatisticsVO> voList = new ArrayList<>();
        for (Map.Entry<String, String> entity : categoryList) {
            PersonalStatisticsVO statisticsVO = new PersonalStatisticsVO();
            AreaListVO vo = map.get(entity.getValue());
            if (Objects.nonNull(vo)) {
                statisticsVO.setAreaName(entity.getKey().replace("派出所", ""));
                statisticsVO.setAreaShowName(entity.getKey());
                statisticsVO.setWarningCount(vo.getWarningCount().longValue());
                voList.add(statisticsVO);
            } else {
                statisticsVO.setAreaCode(entity.getValue());
                statisticsVO.setAreaName(entity.getKey().replace("派出所", ""));
                statisticsVO.setAreaShowName(entity.getKey());
                voList.add(statisticsVO);
            }
        }
        return RestfulResultsV2.ok(voList);
    }

    @Override
    public RestfulResultsV2<PersonVO> personList(PersonDTO dto) {
        dto.setStartTime(null);
        dto.setEndTime(null);
        Page<PersonVO> pageList = shPersonMapper.personList(dto, new Page<>(dto.getPageNum(), dto.getPageSize()));
        pageList.getRecords().forEach(vo -> vo.setAge(DateUtil.getAgeByBirth(vo.getBirthday())));
        return RestfulResultsV2.ok(pageList.getRecords())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(pageList.getTotal());
    }

    @Override
    public RestfulResultsV2<PersonTrackVO> trackList(PersonDTO dto) {
        CurrentUser user = AuthHelper.getCurrentUser();
        // 默认展示1000条轨迹
        final Integer showTrackSize = BeanFactoryHolder.getEnv().getProperty("sh.show.track.size", Integer.class, 1000);
        List<PersonVO> personList = null;
        // 检索某个人
        if (!StringUtils.isEmpty(dto.getSearchValue()) || !StringUtils.isEmpty(dto.getIdCard())) {
            QueryWrapper<Person> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("subject_type", "sh")
                    .and(w -> w.like(StringUtils.isNotEmpty(dto.getSearchValue()), "real_name", dto.getSearchValue())
                            .or()
                            .eq(StringUtils.isNotEmpty(dto.getIdCard()), "id_card", dto.getIdCard()));
            List<Person> persons = shPersonMapper.selectList(queryWrapper);
            personList = persons.stream().map(FxPersonConverter.CONVERTER::doToVO).collect(Collectors.toList());
        } else {
            personList = shPersonMapper.personList(dto, new Page<>(1, showTrackSize)).getRecords();
        }
        String idCards = personList.stream().map(PersonVO::getIdCard).collect(Collectors.joining(";"));
        log.info("人员idCard:{}", idCards);
        if (StringUtils.isEmpty(idCards)) {
            return RestfulResultsV2.ok(new ArrayList<>());
        }
        List<ThemeGjxxbEntity> result = new ArrayList<>();
        Integer partitionSize = BeanFactoryHolder.getEnv().getProperty("sh.track.list.partition.size", Integer.class, 500);
        for (List<String> list : Lists.partition(Arrays.asList(idCards.split(";")), partitionSize)) {
            Expression expression = ExpressionBuilder.And(
                    ExpressionBuilder.Condition("tzlx", Operator.FullTextEqualWholeWord, "身份证"),
                    ExpressionBuilder.Condition("tzzhm", Operator.In, list)
            );
            if (Objects.nonNull(dto.getIdCard())) {
                expression = ExpressionBuilder.And(expression,
                        ExpressionBuilder.Condition("tzzhm", Operator.FullTextEqualWholeWord, dto.getIdCard()),
                        ExpressionBuilder.Condition(StringUtils.isNotEmpty(dto.getStartTime()), "hdsj", Operator.GreaterThanOrEqual, dto.getStartTime()),
                        ExpressionBuilder.Condition(StringUtils.isNotEmpty(dto.getEndTime()), "hdsj", Operator.LessThanOrEqual, dto.getEndTime()),
                        ExpressionBuilder.Condition(StringUtils.isNotEmpty(dto.getGjlx()), "gjlx", Operator.FullTextEqualWholeWord, dto.getGjlx())
                );
            }
            try {
                PageInfo pageInfo = PageInfo.newPage(1, showTrackSize);
                pageInfo.desc("hdsj");
                List<ThemeGjxxbEntity> datas = themeGjxxbRepository.findPageList(expression, pageInfo).getContents();
                log.info("查询的轨迹数：{}", datas.size());
                result.addAll(datas);
            } catch (Exception e) {
                log.error("查询轨迹信息接口失败：", e);
            }
        }
        Map<String, PersonVO> personMap = personList.stream().collect(Collectors.toMap(PersonVO::getIdCard, Function.identity(), (v1, v2) -> v1));
        List<PersonTrackVO> personTrackVos = new ArrayList<>();
        result.forEach(e -> {
            PersonVO personVO = personMap.get(e.getTzzhm());
            if (Objects.isNull(personVO)) {
                return;
            }
            PersonTrackVO vo = PersonTrackVO.of(e, personVO);
            // 根据经纬度获取geoHash
            Integer precise = BeanFactoryHolder.getEnv().getProperty("sh.subject.geoHash.precise", int.class, 6);
            if (!StringUtils.isEmpty(vo.getWdwgs84()) && !StringUtils.isEmpty(vo.getJdwgs84())) {
                vo.setGeoHash(GeoHashUtils.getGeoHash(Double.parseDouble(vo.getWdwgs84()), Double.parseDouble(vo.getJdwgs84()), precise));
            }
            personTrackVos.add(vo);
        });
        if (!CollectionUtils.isEmpty(personTrackVos)) {
            // 根据geohash相同最多的位置,取其中一个位置将focus_flag设置为1
            Map<String, Long> map = personTrackVos.stream()
                    .filter(vo -> StringUtils.isNotEmpty(vo.getGeoHash()))
                    .collect(Collectors.groupingBy(PersonTrackVO::getGeoHash, Collectors.counting()));
            String geoHash = map.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("");
            for (PersonTrackVO vo : personTrackVos) {
                if (geoHash.equals(vo.getGeoHash())) {
                    vo.setFocusFlag(1);
                    break;
                }
            }
        }
        List<PersonTrackVO> results = personTrackVos.stream()
                .sorted(Comparator.comparing(PersonTrackVO::getActivityTime).reversed())
                .limit(showTrackSize)
                .collect(Collectors.toList());
        return RestfulResultsV2.ok(results);
    }

    private Tuple2<String, String> getRatioTime(String startTime, String endTime) {
        LocalDate startDate = LocalDate.parse(startTime);
        LocalDate endDate = LocalDate.parse(endTime);

        Long days = ChronoUnit.DAYS.between(startDate, endDate) + 1L;
        String startRatioTime= TimeUtils.dateBefOrAft(startTime, -days.intValue(), TimeUtils.YYYYMMDD) + " 00:00:00";
        String endRatioTime = TimeUtils.dateBefOrAft(endTime, -days.intValue(), TimeUtils.YYYYMMDD) + " 23:59:59";
        return new Tuple2<>(startRatioTime, endRatioTime);
    }

    private Double computeRatio(Long cCount, Long lCount) {
        if (lCount == 0L) {
            return 0D;
        } else {
            BigDecimal bcCount = new BigDecimal(cCount);
            BigDecimal byCount = new BigDecimal(lCount);
            BigDecimal growthAmount = bcCount.subtract(byCount);
            BigDecimal growthRate = growthAmount.divide(byCount, 4, BigDecimal.ROUND_HALF_UP);
            return growthRate.doubleValue();
        }
    }
}
