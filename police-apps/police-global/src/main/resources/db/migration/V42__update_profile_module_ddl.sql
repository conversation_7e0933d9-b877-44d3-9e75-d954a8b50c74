UPDATE `t_profile_module` SET `cn_name` = '群体材料', `en_name` = 'groupFiles', `type` = 'group', `pid` = 108, `is_archive` = 1, `show_order` = 12, `table_schema` = NULL, `form_schema` = NULL, `list_schema` = NULL, `is_add` = 1, `database_relation` = '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_group_file_relation\", \"column\": \"id\", \"joinTo\": {\"table\": \"t_file_info\", \"column\": \"id\", \"joinColumn\": \"file_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', `show_schema_type` = 'FILE_SCHEMA', `add_schema_type` = 'FILE_SCHEMA', `is_operation_content` = 1 WHERE `id` = 112;
UPDATE `t_profile_module` SET `cn_name` = '相关风险', `en_name` = 'relatedRisk', `type` = 'jq', `pid` = NULL, `is_archive` = 1, `show_order` = 6, `table_schema` = NULL, `form_schema` = NULL, `list_schema` = '{\"name\": \"风险信息\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_risk\", \"fields\": [{\"db\": {\"table\": \"t_risk\", \"column\": \"risk_status\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_status\"}, \"name\": \"risk_status\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"风险状态\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"title\", \"jdbcType\": \"string\"}, \"name\": \"title\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"风险名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"risk_level\", \"mapping\": \"dict_code_to_dict\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_level\"}, \"name\": \"risk_level\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"风险等级\"}, \"colorable\": true, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"risk_code\", \"jdbcType\": \"string\"}, \"name\": \"risk_code\", \"listSchema\": {\"style\": {\"align\": \"center\", \"fixed\": \"left\", \"ellipsis\": true}, \"schema\": {\"type\": \"string\", \"title\": \"风险编号\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"resolve_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_resolve_status\"}, \"name\": \"resolve_status\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"化解结果\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"hand_over_status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_hand_over_status\"}, \"name\": \"hand_over_status\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"化解单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"risk_type\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"string\"}, \"dict\": {\"type\": \"risk_type\"}, \"name\": \"risk_type\", \"listSchema\": {\"style\": {\"align\": \"center\", \"ellipsis\": false}, \"schema\": {\"type\": \"string\", \"title\": \"类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"responsible_dept\", \"mapping\": \"simple_dept_to_dept_name\", \"jdbcType\": \"dept_id\"}, \"name\": \"responsible_dept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"主责单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_risk\", \"column\": \"handle_dept\", \"mapping\": \"simple_dept_to_dept_name\", \"jdbcType\": \"string\"}, \"name\": \"handle_dept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"处置单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}]}', `is_add` = 1, `database_relation` = '{\"type\": \"RELATION_TABLE\", \"table\": \"t_risk_jq_relation\", \"joinTo\": {\"table\": \"t_risk\", \"column\": \"id\", \"joinColumn\": \"risk_id\"}, \"joinFrom\": {\"table\": \"t_profile_jq\", \"column\": \"id\", \"joinColumn\": \"jq_id\"}}', `show_schema_type` = 'LIST_SCHEMA', `add_schema_type` = NULL, `is_operation_content` = 1 WHERE `id` = 506;
UPDATE `t_profile_module` SET `cn_name` = '相关人员', `en_name` = 'relatedPerson', `type` = 'group', `pid` = 108, `is_archive` = 1, `show_order` = 10, `table_schema` = NULL, `form_schema` = NULL, `list_schema` = '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person_group_relation\", \"column\": \"activity_level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\", \"databaseRelation\": {\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}}, \"dict\": {\"type\": \"profile_activity_level\"}, \"name\": \"activityLevel\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"select\", \"title\": \"活跃程度\"}, \"properties\": {\"copyable\": false, \"editable\": true, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', `is_add` = 1, `database_relation` = '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_group_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_group\", \"column\": \"id\", \"joinColumn\": \"group_id\"}}', `show_schema_type` = 'LIST_SCHEMA', `add_schema_type` = 'LIST_SCHEMA', `is_operation_content` = 1 WHERE `id` = 109;
UPDATE `t_profile_module` SET `cn_name` = '涉及人员', `en_name` = 'relatedPerson', `type` = 'event', `pid` = 207, `is_archive` = 1, `show_order` = 5, `table_schema` = NULL, `form_schema` = NULL, `list_schema` = '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', `is_add` = 1, `database_relation` = '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_event_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_event\", \"column\": \"id\", \"joinColumn\": \"event_id\"}}', `show_schema_type` = 'LIST_SCHEMA', `add_schema_type` = 'LIST_SCHEMA', `is_operation_content` = 1 WHERE `id` = 203;
UPDATE `t_profile_module` SET `cn_name` = '涉及人员', `en_name` = 'relatedPerson', `type` = 'clue', `pid` = 309, `is_archive` = 1, `show_order` = 3, `table_schema` = NULL, `form_schema` = NULL, `list_schema` = '{\"name\": \"相关人员\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_person\", \"fields\": [{\"db\": {\"table\": \"t_profile_person\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"人员名称\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"person_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"personLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"personLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&person_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"人员标签\"}, \"schema\": {\"type\": \"array\", \"title\": \"人员标签\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_person\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"createDept\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"createDept\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}], \"selectable\": false, \"searchFields\": [{\"key\": \"name\", \"name\": \"人员名称\"}]}', `is_add` = 1, `database_relation` = '{\"type\": \"RELATION_TABLE\", \"table\": \"t_profile_person_clue_relation\", \"joinTo\": {\"table\": \"t_profile_person\", \"column\": \"id\", \"joinColumn\": \"person_id\"}, \"joinFrom\": {\"table\": \"t_profile_clue\", \"column\": \"id\", \"joinColumn\": \"clue_id\"}}', `show_schema_type` = 'LIST_SCHEMA', `add_schema_type` = 'LIST_SCHEMA', `is_operation_content` = 1 WHERE `id` = 303;
UPDATE `t_profile_module` SET `cn_name` = '档案管理', `en_name` = 'archive', `type` = 'event', `pid` = NULL, `is_archive` = 0, `show_order` = 1, `table_schema` = NULL, `form_schema` = NULL, `list_schema` = '{\"name\": \"事件档案\", \"type\": \"LIST_SCHEMA\", \"table\": \"t_profile_event\", \"fields\": [{\"db\": {\"table\": \"t_profile_event\", \"column\": \"name\", \"jdbcType\": \"string\"}, \"name\": \"name\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件名称\"}, \"properties\": {\"href\": \"/ys-app/archives/event/details?id={value}\", \"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"event_label\", \"mapping\": \"label_id_array_to_name\", \"jdbcType\": \"label_id_array\"}, \"name\": \"eventLabel\", \"listSchema\": {\"style\": {\"align\": \"left\"}, \"filter\": {\"key\": \"eventLabel\", \"type\": \"multiple-tree\", \"value\": [\"&&event_label&&\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"事件类别\"}, \"schema\": {\"type\": \"array\", \"title\": \"事件类别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 2}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"level\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_level\"}, \"name\": \"level\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"filter\": {\"key\": \"level\", \"type\": \"select\", \"value\": [\"%%profile_event_level%%\"], \"fieldNames\": {\"label\": \"name\", \"value\": \"code\"}, \"displayName\": \"事件级别\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件级别\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_time\", \"mapping\": \"date_time_to_general_string\", \"jdbcType\": \"datetime\"}, \"name\": \"relatedTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"指向时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"related_address\", \"mapping\": \"map_location_string\", \"jdbcType\": \"string\"}, \"name\": \"relatedAddress\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"指向地址\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"source\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_source\"}, \"name\": \"source\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件来源\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"status\", \"mapping\": \"dict_code_to_name\", \"jdbcType\": \"number\"}, \"dict\": {\"type\": \"profile_event_status\"}, \"name\": \"status\", \"listSchema\": {\"style\": {\"align\": \"center\"}, \"schema\": {\"type\": \"string\", \"title\": \"事件状态\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false, \"instrLength\": 1}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_dept_id\", \"mapping\": \"dept_id_to_dept_name\", \"jdbcType\": \"number\"}, \"name\": \"create_dept_id\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"filter\": {\"key\": \"create_dept_id\", \"type\": \"tree\", \"value\": [\"&&dept&&\"], \"fieldNames\": {\"label\": \"shortName\", \"value\": \"id\", \"children\": \"children\"}, \"displayName\": \"录入单位\"}, \"schema\": {\"type\": \"string\", \"title\": \"录入单位\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": false}}}, {\"db\": {\"table\": \"t_profile_event\", \"column\": \"create_time\", \"mapping\": \"date_time_to_text\", \"jdbcType\": \"datetime\"}, \"name\": \"createTime\", \"listSchema\": {\"style\": {\"align\": \"center\", \"width\": 120}, \"schema\": {\"type\": \"string\", \"title\": \"录入时间\"}, \"properties\": {\"copyable\": false, \"editable\": false, \"required\": false, \"sortable\": true, \"sortDefault\": \"descending\"}}}], \"selectable\": true, \"searchFields\": [{\"key\": \"name\", \"name\": \"事件名称\"}, {\"key\": \"relatedAddress\", \"name\": \"事发地点\"}]}', `is_add` = 0, `database_relation` = '{}', `show_schema_type` = 'LIST_SCHEMA', `add_schema_type` = 'LIST_SCHEMA', `is_operation_content` = 1 WHERE `id` = 201;


INSERT IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3525, NULL, 'risk_resolve_status_group', NULL, '风险化解状态', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3526, NULL, 'risk_resolve_status', 0, '未化解', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3527, NULL, 'risk_resolve_status', 1, '已化解', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3528, NULL, 'risk_hand_over_status_group', NULL, '风险化解单位', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3529, NULL, 'risk_hand_over_status', 1, '党政化解', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT IGNORE INTO `t_dict` (`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (3530, NULL, 'risk_hand_over_status', 0, '公安化解', NULL, NULL, NULL, NULL, NULL, NULL);
