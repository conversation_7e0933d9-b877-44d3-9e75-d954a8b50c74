-- 新部署的环境群档缺少群体级别
UPDATE t_profile_module
SET
form_schema='{"name":"群体信息","type":"FORM_SCHEMA","table":"t_profile_group","fields":[{"db":{"table":"t_profile_group","column":"name","jdbcType":"string"},"name":"name","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"input","titleLocation":"left"}},"schema":{"type":"string","title":"群体名称"}}},{"db":{"table":"t_profile_group","column":"group_label","jdbcType":"json_id_array"},"name":"groupLabel","tree":{"root":"group","type":"label"},"formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"cascader","multiple":true,"fieldNames":{"label":"name","value":"id","children":"children"},"titleLocation":"left"}},"schema":{"type":"array","items":{"type":"array"},"title":"群体类别"}}},{"db":{"table":"t_profile_group","column":"control_level","jdbcType":"string"},"dict":{"type":"profile_person_control_level"},"name":"control_level","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"radio","titleLocation":"left"}},"schema":{"type":"number","title":"群体级别"}}},{"db":{"table":"t_profile_group","column":"work_target","jdbcType":"string"},"dict":{"type":"profile_work_target"},"name":"work_target","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"radio","titleLocation":"left"}},"schema":{"type":"number","title":"工作目标"}}},{"db":{"table":"t_profile_group","column":"basic_info","jdbcType":"string"},"name":"basic_info","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"textarea","titleLocation":"left"}},"schema":{"type":"string","title":"基本情况"}}},{"db":{"table":"t_profile_group","column":"main_demand","jdbcType":"string"},"name":"main_demand","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"textarea","titleLocation":"left"}},"schema":{"type":"string","title":"风险背景"}}},{"db":{"table":"t_profile_group","column":"work_measures","jdbcType":"string"},"name":"work_measures","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"textarea","titleLocation":"left"}},"schema":{"type":"string","title":"工作措施"}}},{"db":{"table":"t_profile_group","column":"petition_info","jdbcType":"string"},"name":"petition_info","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"textarea","titleLocation":"left"}},"schema":{"type":"string","title":"化解难点"}}},{"db":{"table":"t_profile_group","column":"punish_info","jdbcType":"string"},"name":"punish_info","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"textarea","titleLocation":"left"}},"schema":{"type":"string","title":"维权及打处情况"}}},{"db":{"table":"t_profile_group","column":"realtime_trend","jdbcType":"string"},"name":"realtime_trend","formSchema":{"ui":{"ui:options":{"width":"0.5","widget":"textarea","titleLocation":"left"}},"schema":{"type":"string","title":"现实动向"}}}],"required":["name"]}'
WHERE id = 103;

-- 工作目标的初始化
DELIMITER $$
DROP PROCEDURE IF EXISTS `add_data` $$
CREATE PROCEDURE add_data()
BEGIN
    IF NOT EXISTS( SELECT * FROM  t_dict WHERE `type`='profile_work_target_group' AND name ='工作目标')
    THEN
        INSERT INTO `t_dict`(`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (6093, 6093, 'profile_work_target_group', 0, '工作目标', 0, NULL, NULL, NULL, NULL, NULL);
    END IF;

    IF NOT EXISTS( SELECT * FROM  t_dict WHERE `type`='profile_work_target' AND name='稳控')
    THEN
        INSERT INTO `t_dict`(`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (6094, 6093, 'profile_work_target', 1, '稳控', 0, NULL, NULL, NULL, NULL, NULL);
    END IF;

    IF NOT EXISTS( SELECT * FROM  t_dict WHERE `type`='profile_work_target' AND name='化解')
    THEN
        INSERT INTO `t_dict`(`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (6095, 6093, 'profile_work_target', 2, '化解', 0, NULL, NULL, NULL, NULL, NULL);
    END IF;

    IF NOT EXISTS( SELECT * FROM  t_dict WHERE `type`='profile_work_target' AND name='打击')
    THEN
        INSERT INTO `t_dict`(`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (6096, 6093, 'profile_work_target', 3, '打击', 0, NULL, NULL, NULL, NULL, NULL);
    END IF;

    IF NOT EXISTS( SELECT * FROM  t_dict WHERE `type`='profile_work_target' AND name='训诫')
    THEN
        INSERT INTO `t_dict`(`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (6097, 6093, 'profile_work_target', 4, '训诫', 0, NULL, NULL, NULL, NULL, NULL);
    END IF;

    IF NOT EXISTS( SELECT * FROM  t_dict WHERE `type`='profile_work_target' AND name='关注')
    THEN
        INSERT INTO `t_dict`(`id`, `p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`) VALUES (6098, 6093, 'profile_work_target', 5, '关注', 0, NULL, NULL, NULL, NULL, NULL);
    END IF;
END $$
DELIMITER ;
CALL add_data;
DROP PROCEDURE IF EXISTS `add_data`;

