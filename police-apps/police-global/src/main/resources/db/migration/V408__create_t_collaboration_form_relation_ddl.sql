DROP TABLE IF EXISTS `t_collaboration_form_relation`;
CREATE TABLE `t_collaboration_form_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `collaboration_id` bigint(20) NULL DEFAULT NULL COMMENT '协作id',
  `dept_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '机构代码',
  `year` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '年份',
  `form_number` bigint(20) NULL DEFAULT NULL COMMENT '表单编号',
  `create_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '更新单位主键',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '协作表单关联表' ROW_FORMAT = Dynamic;