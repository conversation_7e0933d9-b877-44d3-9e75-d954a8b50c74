package com.trs.police.global.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.trs.police.common.core.entity.AbstractBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 第三方应用-用户关联
 *
 * <AUTHOR>
 * @date 2023.3.21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_application_third_party_user_relation", autoResultMap = true)
public class ThirdPartyUserRelation extends AbstractBaseEntity {

    private static final long serialVersionUID = 3107992251401029874L;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 单位id
     */
    private Long deptId;

    /**
     * 应用id数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> applicationIds;
}
