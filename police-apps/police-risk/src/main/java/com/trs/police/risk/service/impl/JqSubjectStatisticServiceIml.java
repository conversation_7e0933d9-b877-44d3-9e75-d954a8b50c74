package com.trs.police.risk.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.dto.DeptDto;
import com.trs.police.common.core.entity.CurrentUser;
import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.common.core.utils.NumberUtil;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.common.core.vo.PageResult;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.openfeign.starter.service.PermissionService;
import com.trs.police.risk.config.RiskConfig;
import com.trs.police.risk.domain.dto.JqStatisticAnalysisDTO;
import com.trs.police.risk.domain.dto.JqSubjectStatisticDTO;
import com.trs.police.risk.domain.dto.SubjectSceneContext;
import com.trs.police.risk.domain.entity.RiskPersonFeedback;
import com.trs.police.risk.domain.entity.RiskPersonHandle;
import com.trs.police.risk.domain.vo.*;
import com.trs.police.risk.domain.vo.traceableData.KeyWordVO;
import com.trs.police.risk.mapper.RiskCopyMapper;
import com.trs.police.risk.mapper.RiskMapper;
import com.trs.police.risk.mapper.RiskPersonFeedbackMapper;
import com.trs.police.risk.mapper.RiskPersonHandleMapper;
import com.trs.police.risk.service.JqSubjectStatisticService;
import com.trs.police.risk.service.scene.ISubjectSceneSearch;
import com.trs.police.risk.service.scene.SubjectSceneSearchFactory;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.police.risk.constant.JqStatisticConstant.JQ_STATISTIC_JQ_TYPE_KEY;

/**
 * <AUTHOR>
 * @date 2024/8/13 17:09
 */
@Service
@Slf4j
public class JqSubjectStatisticServiceIml implements JqSubjectStatisticService {

    @Autowired
    private RiskMapper riskMapper;

    @Autowired
    private RiskCopyMapper riskCopyMapper;

    @Autowired
    private RiskPersonFeedbackMapper riskPersonFeedbackMapper;

    @Autowired
    private RiskPersonHandleMapper riskPersonHandleMapper;

    @Autowired
    private RiskConfig riskConfig;

    @Autowired
    private PermissionService permissionService;

    @Override
    public RiskPersonStatisticVO civilToCriminalRiskPerson(JqSubjectStatisticDTO dto, Boolean isHighRisk) throws Exception {
        String scoreLimit = BeanFactoryHolder.getEnv().getProperty("risk.score.limit", "0");
        DeptDto deptDto = permissionService.getDeptByCode(dto.getAreaCode());
        if (Objects.nonNull(deptDto) && (deptDto.getType().equals(1L) || deptDto.getType().equals(2L))){
            dto.setAreaCode(AreaUtils.areaKey(dto.getAreaCode()));
        }
        RiskPersonStatisticVO vo = new RiskPersonStatisticVO();
        //统计风险预警次数
        Long riskWarnCount = riskMapper.statisticRiskWarnCount(dto);
        //民转刑人员数统计
        Long personCount = riskMapper.statisticPersonCount(dto, scoreLimit, isHighRisk, false);
        //民转刑未处理人员数统计
        Long unHandlePersonCount = riskMapper.statisticPersonCount(dto, scoreLimit, isHighRisk, true);
        vo.setRiskWarnCount(riskWarnCount);
        vo.setPersonCount(personCount);
        vo.setUnHandlePersonCount(unHandlePersonCount);
        vo.setStatisticTime(formatTimeRange(dto.getStartTime(), dto.getEndTime()));
        return vo;
    }

    @Override
    public PageResult<PersonCardListVO> civilToCriminalPersonCardList(JqSubjectStatisticDTO dto) {
        String scoreLimit = BeanFactoryHolder.getEnv().getProperty("risk.score.limit", "0");
        DeptDto deptDto = permissionService.getDeptByCode(dto.getAreaCode());
        if (Objects.nonNull(deptDto) && (deptDto.getType().equals(1L) || deptDto.getType().equals(2L))){
            dto.setAreaCode(AreaUtils.areaKey(dto.getAreaCode()));
        }
        Page<Objects> page = Page.of(dto.getPageNum(), dto.getPageSize());
        Page<PersonCardListVO> pageResult = riskMapper.getPersonPhoneNum(dto, scoreLimit, page);
        personCardHandle(pageResult.getRecords(), dto, Boolean.TRUE);
        return PageResult.of(pageResult.getRecords(), dto.getPageNum(), pageResult.getTotal(), dto.getPageSize());
    }

    private void personCardHandle(List<PersonCardListVO> personCardList, JqSubjectStatisticDTO dto, Boolean needGroup) {
        if (CollectionUtils.isEmpty(personCardList)) {
            return;
        }
        List<String> telList = personCardList.stream().map(PersonCardListVO::getTel).collect(Collectors.toList());
        Map<String, String> map = getPersonNameFromPhoneNum(new JqSubjectStatisticDTO(), telList)
                .stream()
                .filter(personCardListVO -> !StringUtils.isNullOrEmpty(personCardListVO.getTel()))
                .filter(personCardListVO -> !"匿名".equals(personCardListVO.getPersonName()))
                .collect(Collectors.toMap(PersonCardListVO::getTel, PersonCardListVO::getPersonName, (v1, v2) -> v1));
        //获取所有的riskId
        List<Long> riskIdList = personCardList.stream()
                .flatMap(person -> Stream.of(person.getRiskIds().split(",")))
                .map(Long::valueOf).distinct()
                .collect(Collectors.toList());
        //根据riskId查询出对应的风险预警信息
        Map<String, List<SimilarRiskWarnVO>> cardListMap = riskMapper.getPersonCardList(riskIdList, needGroup)
                .stream()
                .collect(Collectors.groupingBy(SimilarRiskWarnVO::getTel));
        personCardList.forEach(vo -> {
            List<SimilarRiskWarnVO> list = cardListMap.get(vo.getTel());
            if (StringUtils.isEmpty(vo.getPersonName())){
                vo.setPersonName(map.getOrDefault(vo.getTel(), "未知"));
            }
            if (list != null) {
                List<SimilarRiskWarnVO> riskWarnList = list.stream()
                        .map(item -> {
                            SimilarRiskWarnVO warnVO = new SimilarRiskWarnVO();
                            warnVO.setRiskContent(item.getRiskContent());
                            warnVO.setScore(item.getScore());
                            warnVO.setRiskLevel(item.getRiskLevel());
                            warnVO.setRiskType(item.getRiskType());
                            warnVO.setKeywords(getKeyword(item));
                            return warnVO;
                        }).collect(Collectors.toList());
                vo.setSimilarRiskWarn(riskWarnList);
                vo.setRiskType(getRiskTypes(list));
                vo.setRiskLevels(list.stream().map(SimilarRiskWarnVO::getRiskLevel).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
        });
    }

    @Override
    public PageResult<SimilarRiskListVO> similarRiskList(JqSubjectStatisticDTO dto) {
        Page<Objects> page = Page.of(dto.getPageNum(), dto.getPageSize());
        DeptDto deptDto = permissionService.getDeptByCode(dto.getAreaCode());
        if (Objects.nonNull(deptDto) && (deptDto.getType().equals(1L) || deptDto.getType().equals(2L))){
            dto.setAreaCode(AreaUtils.areaKey(dto.getAreaCode()));
        }
        Page<SimilarRiskListVO> pageResult = riskMapper.similarRiskList(dto, page);
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return PageResult.of(pageResult.getRecords(), dto.getPageNum(), pageResult.getTotal(), dto.getPageSize());
        }
        //获取所有的caseId
        List<Long> caseId = pageResult.getRecords().stream()
                .flatMap(person -> Stream.of(person.getCaseId().split(",")))
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> map = riskMapper.getScoreKeyWords(caseId)
                .stream().collect(Collectors.groupingBy(SimilarRiskListVO::getId, Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> list.stream()
                                .flatMap(v -> v.getScoreKeyWords().stream())
                                .map(KeyWordVO::getName).filter(v -> !StringUtil.isEmpty(v))
                                .distinct()
                                .collect(Collectors.joining(",")))));
        pageResult.getRecords().forEach(vo -> {
            vo.setCopyDept(riskCopyMapper.selectDeptNameByRiskId(vo.getId()));
            // 将 scoreKeyWords 中的 name 属性用逗号分隔并赋值给 keywords
            vo.setKeywords(map.getOrDefault(vo.getId(), ""));
        });
        return PageResult.of(pageResult.getRecords(), dto.getPageNum(), pageResult.getTotal(), dto.getPageSize());
    }

    @Override
    public List<PersonCardListVO> getPersonNameFromPhoneNum(JqSubjectStatisticDTO dto, List<String> telList) {
        return riskMapper.getPersonNameFromPhoneNum(dto.getPhoneNum(), telList);
    }

    /**
     * 获取风险类型
     *
     * @param list list
     * @return 结果
     */
    private static String getRiskTypes(List<SimilarRiskWarnVO> list) {
        return list.stream()
                .map(SimilarRiskWarnVO::getRiskType)
                .distinct() // 如果需要去重的话
                .collect(Collectors.joining(","));
    }

    /**
     * 获取关键词
     *
     * @param item item
     * @return 结果
     */
    private static String getKeyword(SimilarRiskWarnVO item) {
        return item.getScoreKeyWords().stream().map(KeyWordVO::getName).collect(Collectors.joining(","));
    }

    /**
     * 格式化时间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     * @throws Exception 异常
     */
    public static String formatTimeRange(String startTime, String endTime) throws Exception {
        // 定义输入的日期格式
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将字符串解析为Date对象
        Date startDate = inputFormat.parse(startTime);
        Date endDate = inputFormat.parse(endTime);
        // 定义输出的日期格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年M月d日");
        // 将Date对象格式化为指定的字符串格式
        String formattedStartTime = outputFormat.format(startDate);
        String formattedEndTime = outputFormat.format(endDate);
        // 返回格式化后的时间范围
        return formattedStartTime + " - " + formattedEndTime;

    }

    /**
     * 风险类型统计
     *
     * @param dto 查询参数
     * @return 统计结果
     */
    @Override
    public RestfulResultsV2<PoliceSubjectStatisticsVO> jqRiskTypeStatistic(JqStatisticAnalysisDTO dto) {
        DeptDto deptDto = permissionService.getDeptByCode(dto.getAreaCode());
        if (Objects.nonNull(deptDto) && (deptDto.getType().equals(1L) || deptDto.getType().equals(2L))){
            dto.setAreaCode(AreaUtils.areaKey(dto.getAreaCode()));
        }
        ISubjectSceneSearch searchImpl = SubjectSceneSearchFactory.getSubjectSceneSearchImpl(JQ_STATISTIC_JQ_TYPE_KEY);
        SubjectSceneContext<JqStatisticAnalysisDTO> context = new SubjectSceneContext<>();
        context.setDto(dto);
        context.setTimeIsCustom(StringUtil.isEmpty(dto.getTimeIsCustom()) ? "1" : dto.getTimeIsCustom());
        String currentDate = TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD_HHMMSS);
        context.setEndTime("1".equals(context.getTimeIsCustom()) ? dto.getEndTime() : TimeUtils.isMaxDate(currentDate, dto.getEndTime()) ? dto.getEndTime() : currentDate);
        context.setStartTime(dto.getStartTime());
        List search = searchImpl.search(context);
        return RestfulResultsV2.ok(search);
    }

    /**
     * 生成报告
     *
     * @param dto 查询参数
     * @return 报告内容
     */
    @Override
    public RestfulResultsV2<ReportVO> createReport(JqStatisticAnalysisDTO dto, Boolean isHighRisk) throws Exception {
        DeptDto deptDto = permissionService.getDeptByCode(dto.getAreaCode());
        if (Objects.nonNull(deptDto) && (deptDto.getType().equals(1L) || deptDto.getType().equals(2L))){
            dto.setAreaCode(AreaUtils.areaKey(dto.getAreaCode()));
        }
        List<PoliceSubjectStatisticsVO> jqStatisticResult = jqRiskTypeStatistic(dto).getDatas();
        JqSubjectStatisticDTO jqSubjectStatisticDTO = new JqSubjectStatisticDTO();
        jqSubjectStatisticDTO.setStartTime(dto.getStartTime());
        jqSubjectStatisticDTO.setEndTime(dto.getEndTime());
        jqSubjectStatisticDTO.setAreaCode(dto.getAreaCode());
        RiskPersonStatisticVO personStatisticVO = civilToCriminalRiskPerson(jqSubjectStatisticDTO, isHighRisk);
        List<PersonCardListVO> personRiskLevelList = getAllPersonCard(jqSubjectStatisticDTO);
        String jqText = buildJqStatisticText(jqStatisticResult, personRiskLevelList, personStatisticVO, dto);
        ReportVO reportVO = new ReportVO();
        reportVO.setContentTitle(BeanFactoryHolder.getEnv().getProperty("com.trs.police.jqStatistic.contentTitle", "高新分局"));
        reportVO.setSubContentTitle(BeanFactoryHolder.getEnv().getProperty("com.trs.police.jqStatistic.subContentTitle", "总警情分析报告"));
        reportVO.setTitle("新建报告" + TimeUtils.stringToString(TimeUtils.getCurrentDate(), "MMdd"));
        reportVO.setContent(jqText);
        return RestfulResultsV2.ok(reportVO);
    }

    @Override
    public RestfulResultsV2<RiskPersonFeedbackVO> getRiskPersonFeedback(JqSubjectStatisticDTO dto) {
        List<RiskPersonFeedbackVO> list = riskMapper.getRiskPersonFeedback(dto);
        if (CollectionUtils.isEmpty(list)) {
            return RestfulResultsV2.ok(new ArrayList<>());
        }
        return RestfulResultsV2.ok(list)
                .addTotalCount((long) list.size());
    }

    @Override
    public void addRiskPersonFeedback(JqSubjectStatisticDTO dto) {
        try {
            CurrentUser currentUser = AuthHelper.getCurrentUser();
            RiskPersonFeedback riskPersonFeedback = new RiskPersonFeedback();
            riskPersonFeedback.fillAuditFields(currentUser);
            riskPersonFeedback.setBjdh(dto.getPhoneNum());
            ContentVO contentVO = new ContentVO();
            contentVO.setContent(dto.getContent());
            SimpleUserVO simpleUserVO = new SimpleUserVO(currentUser);
            contentVO.setUser(simpleUserVO);
            contentVO.setOperateTime(LocalDateTime.now());
            riskPersonFeedback.setFeedback(contentVO);
            riskPersonFeedbackMapper.insert(riskPersonFeedback);
        } catch (Exception e) {
            log.error("添加反馈失败", e);
            throw new TRSException("添加反馈失败！" + e.getMessage());
        }
    }

    @Override
    public void delRiskPersonFeedback(Long id) {
        try {
            // 获取当前用户信息
            CurrentUser currentUser = AuthHelper.getCurrentUser();
            RiskPersonFeedback riskPersonFeedback = riskPersonFeedbackMapper.selectById(id);
            //验证当前用户是否有权删除此反馈，如果是则可以删除
            if (currentUser.isSelf(riskPersonFeedback.getFeedback().getUser())) {
                riskPersonFeedbackMapper.deleteById(id);
            }
        } catch (Exception e) {
            log.error("删除反馈失败", e);
            throw new TRSException("删除反馈失败！" + e.getMessage());
        }
    }

    @Override
    public void handleRiskPerson(JqSubjectStatisticDTO dto) {
        try {
            // 获取当前用户信息
            CurrentUser currentUser = AuthHelper.getCurrentUser();
            List<RiskPersonHandle> list = riskPersonHandleMapper.selectList(new QueryWrapper<RiskPersonHandle>()
                    .eq("bjdh", dto.getPhoneNum()));
            if (CollectionUtils.isEmpty(list)) {
                RiskPersonHandle riskPersonHandle = new RiskPersonHandle();
                riskPersonHandle.fillAuditFields(currentUser);
                riskPersonHandle.setBjdh(dto.getPhoneNum());
                riskPersonHandle.setStatus(dto.getIsHandle());
                riskPersonHandleMapper.insert(riskPersonHandle);
            }else {
                RiskPersonHandle personHandle = list.get(0);
                personHandle.setUpdateTime(LocalDateTime.now());
                personHandle.setUpdateDeptId(currentUser.getDeptId());
                personHandle.setUpdateUserId(currentUser.getId());
                personHandle.setStatus(dto.getIsHandle());
                riskPersonHandleMapper.updateById(personHandle);
            }
        } catch (Exception e) {
            log.error("处理风险人员失败", e);
            throw new TRSException("处理风险人员失败！" + e.getMessage());
        }
    }

    private List<PersonCardListVO> getAllPersonCard(JqSubjectStatisticDTO jqSubjectStatisticDTO) {
        List<PersonCardListVO> personCardListVos = riskMapper.getPersonPhoneNoPage(jqSubjectStatisticDTO);
        personCardHandle(personCardListVos, jqSubjectStatisticDTO, Boolean.FALSE);
        return personCardListVos;
    }

    /**
     * 根据统计结果构造文本
     *
     * @param jqStatisticResult   警情分析结果
     * @param personRiskLevelList 风险人员结果
     * @param personStatisticVO   风险人员结果
     * @param dto                 查询参数
     * @return 拼接文本
     */
    private String buildJqStatisticText(List<PoliceSubjectStatisticsVO> jqStatisticResult, List<PersonCardListVO> personRiskLevelList, RiskPersonStatisticVO personStatisticVO, JqStatisticAnalysisDTO dto) {
        PoliceSubjectStatisticsVO statisticsVO = jqStatisticResult.stream()
                .filter(vo -> "all".equals(vo.getKey()))
                .findFirst().get();
        StringBuilder builder = new StringBuilder();
        String first = String.format("%s至%s，分局接报纠纷类警情%s件，环比%s件%s%s",
                dataConvert(dto.getStartTime()), dataConvert(dto.getEndTime()), statisticsVO.getCount(), statisticsVO.getRecentCount(), statisticsVO.getRatio() >= 0D ? "上升" : "下降", NumberUtil.doubleToHundredPercent(Math.abs(statisticsVO.getRatio())));
        builder.append(first);
        String pcsName = String.join("、", statisticsVO.getRatioUpDeptList());
        if (!StringUtils.isEmpty(pcsName)) {
            builder.append(String.format("，%s所上升。", pcsName));
        } else {
            builder.append("。");
        }
        String second = jqStatisticResult.stream()
                .filter(vo -> !Arrays.asList("all", "jfAll").contains(vo.getKey()) && !CollectionUtils.isEmpty(vo.getRatioUpDeptList()))
                .map(vo -> String.format("%s环比上升的派出所有：%s所；", vo.getName(), String.join("、", vo.getRatioUpDeptList())))
                .collect(Collectors.joining("\n"));
        if (!StringUtils.isEmpty(second)) {
            second = second.substring(0, second.length() - 1);
            builder.append("\n").append("其中").append(second).append("。");
        }
        String third = String.format("%s至%s共出现民转刑风险预警%s次，涉及民转刑风险人员%s人",
                dataConvert(dto.getStartTime()), dataConvert(dto.getEndTime()), personStatisticVO.getRiskWarnCount(), personStatisticVO.getPersonCount());
        builder.append("\n").append(third);
        String riskLevelText = buildPersonRiskLevelText(personRiskLevelList);
        if (StringUtils.isEmpty(riskLevelText)) {
            builder.append("。");
        } else {
            builder.append("；").append(riskLevelText).append("。");
        }
        return builder.toString();
    }

    private String buildPersonRiskLevelText(List<PersonCardListVO> personRiskLevelList) {
        Map<Long, String> riskLevelMap = getRiskLevelMap();
        if (riskLevelMap.isEmpty()) {
            return "";
        }
        String riskLevelText = riskLevelMap.entrySet().stream()
                .map(entry -> {
                    Map<Boolean, List<String>> map = personRiskLevelList.stream()
                            .filter(vo -> vo.getRiskLevels().contains(entry.getKey()))
                            .map(PersonCardListVO::getPersonName)
                            .collect(Collectors.groupingBy("未知"::equals));
                    return map.isEmpty()
                            ? String.format("%s人员共有0人", entry.getValue())
                            : buildPersonName(entry.getValue(), map);
                }).collect(Collectors.joining("；"));
        if (StringUtils.isEmpty(riskLevelText)) {
            return "";
        }
        return riskLevelText;
    }

    private String buildPersonName(String riskLevel, Map<Boolean, List<String>> map) {
        List<String> unKnowName = map.getOrDefault(Boolean.TRUE, Collections.emptyList());
        List<String> knowName = map.getOrDefault(Boolean.FALSE, Collections.emptyList());
        Integer count = unKnowName.size() + knowName.size();
        return String.format("%s人员共有%s人%s", riskLevel, count,
                knowName.isEmpty() ? "" : String.format(":%s", String.join("、", knowName)));
    }

    private Map<Long, String> getRiskLevelMap() {
        Map<Long, String> map = new HashMap<>();
        for (String string : riskConfig.mzxRiskLevel.split(";")) {
            String[] strings = string.split(":");
            if (strings.length == 2) {
                map.put(Long.valueOf(strings[0]), strings[1]);
            }
        }
        return map;
    }

    private String dataConvert(String data) {
        return TimeUtils.stringToString(TimeUtils.YYYYMMDD_HHMMSS, data, TimeUtils.YYYYMMDD2);
    }

}
