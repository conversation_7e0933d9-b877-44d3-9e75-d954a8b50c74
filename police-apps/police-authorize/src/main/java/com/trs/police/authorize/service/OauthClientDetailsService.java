package com.trs.police.authorize.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.police.authorize.domain.entity.OauthClientDetails;
import java.util.List;
import org.springframework.security.oauth2.provider.ClientAlreadyExistsException;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.oauth2.provider.NoSuchClientException;

/**
 * oauth2客户端
 *
 * <AUTHOR>
 */
public interface OauthClientDetailsService {

    /**
     * 根据id加载客户端
     *
     * @param clientId 用户id
     * @return 用户信息
     * @throws ClientRegistrationException 用户注册异常
     */
    ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException;

    /**
     * 添加客户端
     *
     * @param clientDetails 用户信息
     * @throws ClientAlreadyExistsException 用户已存在异常
     */
    void addClientDetails(ClientDetails clientDetails) throws ClientAlreadyExistsException;

    /**
     * 更新客户端
     *
     * @param clientDetails 用户信息
     * @throws NoSuchClientException 用户不存在异常
     */
    void updateClientDetails(ClientDetails clientDetails) throws NoSuchClientException;

    /**
     * 更新客户端密码
     *
     * @param clientId 用户id
     * @param secret   密码
     * @throws NoSuchClientException 用户不存在异常
     */
    void updateClientSecret(String clientId, String secret) throws NoSuchClientException;

    /**
     * 移除客户端
     *
     * @param clientId 用户id
     * @throws NoSuchClientException 用户不存在异常
     */
    void removeClientDetails(String clientId) throws NoSuchClientException;

    /**
     * 查询用户信息列表
     *
     * @return 用户信息
     */
    List<ClientDetails> listClientDetails();

    /**
     * 分页查询用户信息列表
     *
     * @param page 分页参数
     * @return 用户信息
     */
    Page<OauthClientDetails> pageAll(Page<OauthClientDetails> page);

    /**
     * 生成客户端密码
     *
     * @param secret 密码
     * @return 客户端密码
     * @throws NoSuchClientException 用户不存在异常
     */
    String generateClientSecret(String secret) throws NoSuchClientException;
}
