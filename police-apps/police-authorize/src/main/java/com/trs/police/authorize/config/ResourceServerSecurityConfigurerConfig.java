package com.trs.police.authorize.config;

import com.trs.police.common.security.starter.converter.CustomResourceUserAuthenticationConverter;
import com.trs.police.common.security.starter.handler.CommonAccessDeniedHandler;
import com.trs.police.common.security.starter.handler.CommonAuthExceptionEntryPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;

import javax.annotation.Resource;

/**
 * OAuth2 Filter 配置类
 * *@author:wen.wen
 * *@create 2024-07-10 17:36
 **/
@Configuration
public class ResourceServerSecurityConfigurerConfig {

    @Autowired(required = false)
    private CommonAccessDeniedHandler accessDeniedHandler;

    @Autowired(required = false)
    private CommonAuthExceptionEntryPoint exceptionEntryPoint;

    @Autowired(required = false)
    private RemoteTokenServices remoteTokenServices;

    @Autowired(required = false)
    private AuthenticationEventPublisher eventPublisher;

    @Resource
    private ApplicationContext applicationContext;


    /**
     * 生成ResourceServerSecurityConfigurer，该类包含有oauth2相关操作行为
     *
     * @return ResourceServerSecurityConfigurer
     */
    public ResourceServerSecurityConfigurer configure() {
        ResourceServerSecurityConfigurer resources = new ResourceServerSecurityConfigurer();
        resources.tokenStore(applicationContext.getBean(TokenStore.class));
        if (eventPublisher != null) {
            resources.eventPublisher(eventPublisher);
        }
        if (exceptionEntryPoint != null) {
            resources.authenticationEntryPoint(exceptionEntryPoint);
        }
        if (accessDeniedHandler != null) {
            resources.accessDeniedHandler(accessDeniedHandler);
        }
        if (remoteTokenServices != null) {
            DefaultAccessTokenConverter tokenConverter = new DefaultAccessTokenConverter();
            tokenConverter.setUserTokenConverter(new CustomResourceUserAuthenticationConverter());
            remoteTokenServices.setAccessTokenConverter(tokenConverter);
            resources.tokenServices(remoteTokenServices);
        }
        return resources;
    }
}
