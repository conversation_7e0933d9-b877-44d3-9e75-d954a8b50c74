package com.trs.police.authorize.authorization.remote;

import com.trs.police.authorize.domain.entity.Dept;
import com.trs.police.authorize.service.DeptService;
import com.trs.police.authorize.service.UserService;
import com.trs.police.common.core.entity.CurrentUser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 从其他环境ys跳转登录
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@Data
@Slf4j
public class YsAuthenticationProvider implements AuthenticationProvider {

    private static final String LOGIN_TYPE = "ys";
    
    private UserService userDetailsService;

    private DeptService deptService;
    

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        YsAuthenticationToken token = (YsAuthenticationToken) authentication;
        
        try {
            CurrentUser currentUser = (CurrentUser) token.getPrincipal();
            // 使用远程用户的用户名在本地系统中加载用户
            UserDetails localUser = userDetailsService.loadUserByUsername(currentUser.getUsername());
            if (localUser == null) {
                throw new InternalAuthenticationServiceException("本地用户"+currentUser.getUsername()+"不存在");
            }
            Dept dept = deptService.getDeptByCode(currentUser.getDeptCode());
            if (dept == null) {
                throw new InternalAuthenticationServiceException("本地部门"+currentUser.getDept().getName()+"不存在");
            }
            // 创建认证令牌
            YsAuthenticationToken authenticationToken = new YsAuthenticationToken(
                localUser, localUser.getAuthorities(), dept.getId().toString());
            
            authenticationToken.setDetails(token.getDetails());
            
            return authenticationToken;
            
        } catch (Exception e) {
            log.error("ys认证失败", e);
            throw new InternalAuthenticationServiceException("ys认证失败: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return YsAuthenticationToken.class.isAssignableFrom(authentication);
    }
} 